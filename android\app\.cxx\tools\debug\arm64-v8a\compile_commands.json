[{"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\Al_Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dappmodules_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\1c9bc7a706f1aa34c1144f6b14d8ea73\\components\\RNCSlider\\RNCSliderMeasurementsManager.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderMeasurementsManager.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderMeasurementsManager.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\2d43be78a5814557fb9dfebf389efa55\\renderer\\components\\RNCSlider\\RNCSliderShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\common\\cpp\\react\\renderer\\components\\RNCSlider\\RNCSliderShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\450e96dfadc67fceb097ed9f86c4d42c\\build\\generated\\source\\codegen\\jni\\RNCSlider-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\RNCSlider-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\RNCSlider-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\d48b02f5335ddb98394dd701ac251f43\\renderer\\components\\RNCSlider\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\ec7c735bd696b2e703dc38af20613a63\\jni\\react\\renderer\\components\\RNCSlider\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\682f7ecb217b40b3d813aad9b84153a4\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\d48b02f5335ddb98394dd701ac251f43\\renderer\\components\\RNCSlider\\RNCSliderJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\RNCSliderJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\RNCSliderJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\ec7c735bd696b2e703dc38af20613a63\\jni\\react\\renderer\\components\\RNCSlider\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_RNCSlider_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCSlider_autolinked_build\\CMakeFiles\\react_codegen_RNCSlider.dir\\682f7ecb217b40b3d813aad9b84153a4\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCSlider\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ea538b5f6a083ae481ed194c783d0df6\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ea538b5f6a083ae481ed194c783d0df6\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\5b164d8606005cae4cb4afbdd6749b21\\safeareacontext\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\90494090ce81146405a5b07366c7a0b7\\components\\safeareacontext\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\35fce22f7da4491a88be18cc12a429c2\\renderer\\components\\safeareacontext\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\90494090ce81146405a5b07366c7a0b7\\components\\safeareacontext\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\35fce22f7da4491a88be18cc12a429c2\\renderer\\components\\safeareacontext\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\fc74f1e8c391aa09fb02e128a42ef902\\safeareacontextJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_safeareacontext_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\585fc25720df4ef3bb62349005083b4d\\codegen\\jni\\safeareacontext-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ee5220fe13787df5bff135edc4d32ae2\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\38baf01cee6db3bd3c6370a8836ed8c4\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\38baf01cee6db3bd3c6370a8836ed8c4\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5acfc6ee6c1fc2f330658b445322d225\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ee5220fe13787df5bff135edc4d32ae2\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\5acfc6ee6c1fc2f330658b445322d225\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ee5220fe13787df5bff135edc4d32ae2\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6227d8a94c0befa77eb1be8991cf37a7\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b752d79e726ffcb224bd99ed6530af3a\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\86707fa43f9e1dc1b991ce78dbb54a37\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9ca9557db7350d0cef645e2c53ffcf82\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\86707fa43f9e1dc1b991ce78dbb54a37\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\9ca9557db7350d0cef645e2c53ffcf82\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -Dreact_codegen_rnscreens_EXPORTS -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b752d79e726ffcb224bd99ed6530af3a\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\796ad459e5c56493b6ccb0d610a9a963\\RNVectorIconsSpecJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ComponentDescriptors.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\EventEmitters.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\Props.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\Props.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\ShadowNodes.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\States.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\States.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnworklets\\rnworkletsJSI-generated.cpp"}, {"directory": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\271~1.122\\TOOLCH~1\\llvm\\prebuilt\\WINDOW~1\\bin\\CLANG_~1.EXE --target=aarch64-none-linux-android24 --sysroot=\"C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/.\" -I\"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include\" -isystem \"C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include\" -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnworklets_autolinked_build\\CMakeFiles\\react_codegen_rnworklets.dir\\rnworklets-generated.cpp.o -c \"D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp\"", "file": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\rnworklets-generated.cpp"}]