# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCSlider cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnworklets cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/D_/Al_Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\D_\Al_Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\D_\Al_Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libappmodules.pdb"

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libappmodules.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libappmodules.so

build D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o CMakeFiles/appmodules.dir/D_/Al_Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rnreanimated_autolinked_build/react_codegen_rnreanimated rnworklets_autolinked_build/react_codegen_rnworklets
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so"  "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so"  "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libappmodules.so"
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libappmodules.pdb"
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnasyncstorage_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnasyncstorage_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_RNCSlider


#############################################
# Order-only phony target for react_codegen_RNCSlider

build cmake_object_order_depends_target_react_codegen_RNCSlider: phony || RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/1c9bc7a706f1aa34c1144f6b14d8ea73/components/RNCSlider/RNCSliderMeasurementsManager.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderMeasurementsManager.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\1c9bc7a706f1aa34c1144f6b14d8ea73\components\RNCSlider\RNCSliderMeasurementsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\1c9bc7a706f1aa34c1144f6b14d8ea73\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/2d43be78a5814557fb9dfebf389efa55/renderer/components/RNCSlider/RNCSliderShadowNode.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/common/cpp/react/renderer/components/RNCSlider/RNCSliderShadowNode.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\2d43be78a5814557fb9dfebf389efa55\renderer\components\RNCSlider\RNCSliderShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\2d43be78a5814557fb9dfebf389efa55\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/450e96dfadc67fceb097ed9f86c4d42c/build/generated/source/codegen/jni/RNCSlider-generated.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/RNCSlider-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\450e96dfadc67fceb097ed9f86c4d42c\build\generated\source\codegen\jni\RNCSlider-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\450e96dfadc67fceb097ed9f86c4d42c\build\generated\source\codegen\jni
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/d48b02f5335ddb98394dd701ac251f43/renderer/components/RNCSlider/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\d48b02f5335ddb98394dd701ac251f43\renderer\components\RNCSlider\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\d48b02f5335ddb98394dd701ac251f43\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/ec7c735bd696b2e703dc38af20613a63/jni/react/renderer/components/RNCSlider/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\ec7c735bd696b2e703dc38af20613a63\jni\react\renderer\components\RNCSlider\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\ec7c735bd696b2e703dc38af20613a63\jni\react\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/682f7ecb217b40b3d813aad9b84153a4/codegen/jni/react/renderer/components/RNCSlider/Props.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\682f7ecb217b40b3d813aad9b84153a4\codegen\jni\react\renderer\components\RNCSlider\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\682f7ecb217b40b3d813aad9b84153a4\codegen\jni\react\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/d48b02f5335ddb98394dd701ac251f43/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\d48b02f5335ddb98394dd701ac251f43\renderer\components\RNCSlider\RNCSliderJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\d48b02f5335ddb98394dd701ac251f43\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/ec7c735bd696b2e703dc38af20613a63/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\ec7c735bd696b2e703dc38af20613a63\jni\react\renderer\components\RNCSlider\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\ec7c735bd696b2e703dc38af20613a63\jni\react\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"

build RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/682f7ecb217b40b3d813aad9b84153a4/codegen/jni/react/renderer/components/RNCSlider/States.cpp.o: CXX_COMPILER__react_codegen_RNCSlider_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/build/generated/source/codegen/jni/react/renderer/components/RNCSlider/States.cpp || cmake_object_order_depends_target_react_codegen_RNCSlider
  DEFINES = -Dreact_codegen_RNCSlider_EXPORTS
  DEP_FILE = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\682f7ecb217b40b3d813aad9b84153a4\codegen\jni\react\renderer\components\RNCSlider\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/RNCSlider" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  OBJECT_FILE_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\682f7ecb217b40b3d813aad9b84153a4\codegen\jni\react\renderer\components\RNCSlider
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_RNCSlider


#############################################
# Link the shared library D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.so

build D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_RNCSlider_Debug RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/1c9bc7a706f1aa34c1144f6b14d8ea73/components/RNCSlider/RNCSliderMeasurementsManager.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/2d43be78a5814557fb9dfebf389efa55/renderer/components/RNCSlider/RNCSliderShadowNode.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/450e96dfadc67fceb097ed9f86c4d42c/build/generated/source/codegen/jni/RNCSlider-generated.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/d48b02f5335ddb98394dd701ac251f43/renderer/components/RNCSlider/ComponentDescriptors.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/ec7c735bd696b2e703dc38af20613a63/jni/react/renderer/components/RNCSlider/EventEmitters.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/682f7ecb217b40b3d813aad9b84153a4/codegen/jni/react/renderer/components/RNCSlider/Props.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/d48b02f5335ddb98394dd701ac251f43/renderer/components/RNCSlider/RNCSliderJSI-generated.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/ec7c735bd696b2e703dc38af20613a63/jni/react/renderer/components/RNCSlider/ShadowNodes.cpp.o RNCSlider_autolinked_build/CMakeFiles/react_codegen_RNCSlider.dir/682f7ecb217b40b3d813aad9b84153a4/codegen/jni/react/renderer/components/RNCSlider/States.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_RNCSlider.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = RNCSlider_autolinked_build\CMakeFiles\react_codegen_RNCSlider.dir\
  TARGET_FILE = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.so"
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_RNCSlider.pdb"


#############################################
# Utility command for edit_cache

build RNCSlider_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\RNCSlider_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCSlider_autolinked_build/edit_cache: phony RNCSlider_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCSlider_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\RNCSlider_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCSlider_autolinked_build/rebuild_cache: phony RNCSlider_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnreanimated_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnreanimated_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ea538b5f6a083ae481ed194c783d0df6/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ea538b5f6a083ae481ed194c783d0df6\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ea538b5f6a083ae481ed194c783d0df6\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ea538b5f6a083ae481ed194c783d0df6/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ea538b5f6a083ae481ed194c783d0df6\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ea538b5f6a083ae481ed194c783d0df6\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5b164d8606005cae4cb4afbdd6749b21/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\5b164d8606005cae4cb4afbdd6749b21\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\5b164d8606005cae4cb4afbdd6749b21\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90494090ce81146405a5b07366c7a0b7/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90494090ce81146405a5b07366c7a0b7\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90494090ce81146405a5b07366c7a0b7\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/35fce22f7da4491a88be18cc12a429c2/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\35fce22f7da4491a88be18cc12a429c2\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\35fce22f7da4491a88be18cc12a429c2\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90494090ce81146405a5b07366c7a0b7/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90494090ce81146405a5b07366c7a0b7\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\90494090ce81146405a5b07366c7a0b7\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/35fce22f7da4491a88be18cc12a429c2/renderer/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\35fce22f7da4491a88be18cc12a429c2\renderer\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\35fce22f7da4491a88be18cc12a429c2\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fc74f1e8c391aa09fb02e128a42ef902/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fc74f1e8c391aa09fb02e128a42ef902\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\fc74f1e8c391aa09fb02e128a42ef902
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/585fc25720df4ef3bb62349005083b4d/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\585fc25720df4ef3bb62349005083b4d\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\585fc25720df4ef3bb62349005083b4d\codegen\jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.so

build D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ea538b5f6a083ae481ed194c783d0df6/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ea538b5f6a083ae481ed194c783d0df6/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/5b164d8606005cae4cb4afbdd6749b21/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90494090ce81146405a5b07366c7a0b7/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/35fce22f7da4491a88be18cc12a429c2/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/90494090ce81146405a5b07366c7a0b7/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/35fce22f7da4491a88be18cc12a429c2/renderer/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/fc74f1e8c391aa09fb02e128a42ef902/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/585fc25720df4ef3bb62349005083b4d/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_FILE = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.so"
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\safeareacontext_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\safeareacontext_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ee5220fe13787df5bff135edc4d32ae2/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ee5220fe13787df5bff135edc4d32ae2\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ee5220fe13787df5bff135edc4d32ae2\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/38baf01cee6db3bd3c6370a8836ed8c4/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\38baf01cee6db3bd3c6370a8836ed8c4\renderer\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\38baf01cee6db3bd3c6370a8836ed8c4\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/38baf01cee6db3bd3c6370a8836ed8c4/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\38baf01cee6db3bd3c6370a8836ed8c4\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\38baf01cee6db3bd3c6370a8836ed8c4\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5acfc6ee6c1fc2f330658b445322d225/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5acfc6ee6c1fc2f330658b445322d225\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5acfc6ee6c1fc2f330658b445322d225\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ee5220fe13787df5bff135edc4d32ae2/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ee5220fe13787df5bff135edc4d32ae2\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ee5220fe13787df5bff135edc4d32ae2\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5acfc6ee6c1fc2f330658b445322d225/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5acfc6ee6c1fc2f330658b445322d225\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\5acfc6ee6c1fc2f330658b445322d225\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ee5220fe13787df5bff135edc4d32ae2/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ee5220fe13787df5bff135edc4d32ae2\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\ee5220fe13787df5bff135edc4d32ae2\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6227d8a94c0befa77eb1be8991cf37a7/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6227d8a94c0befa77eb1be8991cf37a7\cpp\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6227d8a94c0befa77eb1be8991cf37a7\cpp\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b752d79e726ffcb224bd99ed6530af3a/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b752d79e726ffcb224bd99ed6530af3a\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b752d79e726ffcb224bd99ed6530af3a\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/86707fa43f9e1dc1b991ce78dbb54a37/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\86707fa43f9e1dc1b991ce78dbb54a37\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\86707fa43f9e1dc1b991ce78dbb54a37\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9ca9557db7350d0cef645e2c53ffcf82/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9ca9557db7350d0cef645e2c53ffcf82\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9ca9557db7350d0cef645e2c53ffcf82\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/86707fa43f9e1dc1b991ce78dbb54a37/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\86707fa43f9e1dc1b991ce78dbb54a37\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\86707fa43f9e1dc1b991ce78dbb54a37\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9ca9557db7350d0cef645e2c53ffcf82/codegen/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9ca9557db7350d0cef645e2c53ffcf82\codegen\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\9ca9557db7350d0cef645e2c53ffcf82\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b752d79e726ffcb224bd99ed6530af3a/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b752d79e726ffcb224bd99ed6530af3a\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\b752d79e726ffcb224bd99ed6530af3a\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.so

build D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ee5220fe13787df5bff135edc4d32ae2/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/38baf01cee6db3bd3c6370a8836ed8c4/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/38baf01cee6db3bd3c6370a8836ed8c4/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5acfc6ee6c1fc2f330658b445322d225/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ee5220fe13787df5bff135edc4d32ae2/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/5acfc6ee6c1fc2f330658b445322d225/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/ee5220fe13787df5bff135edc4d32ae2/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6227d8a94c0befa77eb1be8991cf37a7/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b752d79e726ffcb224bd99ed6530af3a/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/86707fa43f9e1dc1b991ce78dbb54a37/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9ca9557db7350d0cef645e2c53ffcf82/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/86707fa43f9e1dc1b991ce78dbb54a37/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/9ca9557db7350d0cef645e2c53ffcf82/codegen/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/b752d79e726ffcb224bd99ed6530af3a/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_FILE = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.so"
  TARGET_PDB = "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\cxx\Debug\q4e5d6l3\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnscreens_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnscreens_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNVectorIconsSpec


#############################################
# Order-only phony target for react_codegen_RNVectorIconsSpec

build cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec: phony || RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/RNVectorIconsSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\RNVectorIconsSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/RNVectorIconsSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963\RNVectorIconsSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\796ad459e5c56493b6ccb0d610a9a963
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""

build RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNVectorIconsSpec_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNVectorIconsSpec
  DEP_FILE = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir
  OBJECT_FILE_DIR = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\react\renderer\components\RNVectorIconsSpec
  TARGET_COMPILE_PDB = RNVectorIconsSpec_autolinked_build\CMakeFiles\react_codegen_RNVectorIconsSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNVectorIconsSpec

build RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/RNVectorIconsSpec-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/EventEmitters.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/Props.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/796ad459e5c56493b6ccb0d610a9a963/RNVectorIconsSpecJSI-generated.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/ShadowNodes.cpp.o RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/react/renderer/components/RNVectorIconsSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\RNVectorIconsSpec_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNVectorIconsSpec_autolinked_build/edit_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\RNVectorIconsSpec_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNVectorIconsSpec_autolinked_build/rebuild_cache: phony RNVectorIconsSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnworklets


#############################################
# Order-only phony target for react_codegen_rnworklets

build cmake_object_order_depends_target_react_codegen_rnworklets: phony || rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/Props.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/States.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets\rnworkletsJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\react\renderer\components\rnworklets
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""

build rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o: CXX_COMPILER__react_codegen_rnworklets_Debug D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/rnworklets-generated.cpp || cmake_object_order_depends_target_react_codegen_rnworklets
  DEP_FILE = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\rnworklets-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/." -I"D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/react/renderer/components/rnworklets" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/e80cc6deab05b24bdfe1060903f43f89/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/d644ee9b842757375fb9cebdd915ee04/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  OBJECT_FILE_DIR = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir
  TARGET_COMPILE_PDB = rnworklets_autolinked_build\CMakeFiles\react_codegen_rnworklets.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnworklets

build rnworklets_autolinked_build/react_codegen_rnworklets: phony rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ComponentDescriptors.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/EventEmitters.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/Props.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/ShadowNodes.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/States.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/react/renderer/components/rnworklets/rnworkletsJSI-generated.cpp.o rnworklets_autolinked_build/CMakeFiles/react_codegen_rnworklets.dir/rnworklets-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnworklets_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnworklets_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnworklets_autolinked_build/edit_cache: phony rnworklets_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnworklets_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a\rnworklets_autolinked_build" && "C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe" --regenerate-during-build -S"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\.cxx\Debug\q4e5d6l3\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnworklets_autolinked_build/rebuild_cache: phony rnworklets_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libappmodules.so

build libappmodules.so: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libappmodules.so

build libreact_codegen_RNCSlider.so: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so

build libreact_codegen_rnscreens.so: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_safeareacontext.so: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react_codegen_RNCSlider: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so

build react_codegen_RNVectorIconsSpec: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_rnworklets: phony rnworklets_autolinked_build/react_codegen_rnworklets

build react_codegen_safeareacontext: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a

build all: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libappmodules.so rnasyncstorage_autolinked_build/all RNCSlider_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all RNVectorIconsSpec_autolinked_build/all rnworklets_autolinked_build/all

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/RNCSlider_autolinked_build

build RNCSlider_autolinked_build/all: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_RNCSlider.so

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/RNVectorIconsSpec_autolinked_build

build RNVectorIconsSpec_autolinked_build/all: phony RNVectorIconsSpec_autolinked_build/react_codegen_RNVectorIconsSpec

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/rnworklets_autolinked_build

build rnworklets_autolinked_build/all: phony rnworklets_autolinked_build/react_codegen_rnworklets

# =============================================================================

#############################################
# Folder: D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/intermediates/cxx/Debug/q4e5d6l3/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/CMakeFiles/VerifyGlobs.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a/CMakeFiles/VerifyGlobs.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt D$:/Al$ Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
