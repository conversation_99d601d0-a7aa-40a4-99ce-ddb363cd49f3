import './global.css';
import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { AudioPlayerProvider } from './src/contexts/AudioPlayerContext';
import { AppNavigator } from './src/navigation/AppNavigator';
import { useTheme } from './src/contexts/ThemeContext';

const AppContent = () => {
  const { isDarkMode } = useTheme();
  
  return (
    <>
      <StatusBar style={isDarkMode ? 'light' : 'dark'} />
      <AppNavigator />
    </>
  );
};

export default function App() {
  return (
    <ThemeProvider>
      <AudioPlayerProvider>
        <AppContent />
      </AudioPlayerProvider>
    </ThemeProvider>
  );
}
