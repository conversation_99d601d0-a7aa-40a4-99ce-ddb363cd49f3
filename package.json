{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@supabase/supabase-js": "^2.56.1", "expo": "~53.0.22", "expo-av": "~15.1.7", "expo-dev-client": "~5.2.4", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "^3.7.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-track-player": "^4.1.2", "react-native-vector-icons": "^10.3.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}