import { createClient } from '@supabase/supabase-js';
import { Audio, Preacher, Category, Playlist, PlaylistAudio } from '../types';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'VOTRE_URL_SUPABASE';
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'VOTRE_CLE_SUPABASE';

export const supabase = createClient(supabaseUrl, supabaseKey);

export const preacherService = {
  async getAll(): Promise<Preacher[]> {
    const { data, error } = await supabase
      .from('preachers')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Preacher | null> {
    const { data, error } = await supabase
      .from('preachers')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }
};

export const categoryService = {
  async getAll(): Promise<Category[]> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Category | null> {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  }
};

export const audioService = {
  async getLatest(limit: number = 10): Promise<Audio[]> {
    const { data, error } = await supabase
      .from('audios')
      .select(`
        *,
        preacher:preachers(*),
        category:categories(*)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) throw error;
    return data || [];
  },

  async getByPreacher(preacherId: string): Promise<Audio[]> {
    const { data, error } = await supabase
      .from('audios')
      .select(`
        *,
        preacher:preachers(*),
        category:categories(*)
      `)
      .eq('preacher_id', preacherId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getByCategory(categoryId: string): Promise<Audio[]> {
    const { data, error } = await supabase
      .from('audios')
      .select(`
        *,
        preacher:preachers(*),
        category:categories(*)
      `)
      .eq('category_id', categoryId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  }
};

export const playlistService = {
  async getAll(): Promise<Playlist[]> {
    const { data, error } = await supabase
      .from('playlists')
      .select(`
        *,
        preacher:preachers(*)
      `)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Playlist | null> {
    const { data, error } = await supabase
      .from('playlists')
      .select(`
        *,
        preacher:preachers(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async getAudios(playlistId: string): Promise<Audio[]> {
    const { data, error } = await supabase
      .from('playlist_audios')
      .select(`
        *,
        audio:audios(
          *,
          preacher:preachers(*),
          category:categories(*)
        )
      `)
      .eq('playlist_id', playlistId)
      .order('position');
    
    if (error) throw error;
    return data?.map(item => item.audio).filter(Boolean) || [];
  }
};
