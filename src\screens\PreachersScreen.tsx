import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, RefreshControl } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { PreacherCard } from '../components/PreacherCard';
import { preacherService } from '../services/supabase';
import { Preacher } from '../types';

export const PreachersScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [preachers, setPreachers] = useState<Preacher[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPreachers();
  }, []);

  const loadPreachers = async () => {
    try {
      setLoading(true);
      const data = await preacherService.getAll();
      setPreachers(data);
    } catch (error) {
      console.error('Erreur lors du chargement des prédicateurs:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPreachers();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <View className={`flex-1 justify-center items-center ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}>
        <Text className={`text-base ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Chargement...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      className={`flex-1 ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View className="p-4">
        <Text className={`text-xl font-bold mb-6 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Prédicateurs
        </Text>
        
        {preachers.map((preacher) => (
          <PreacherCard key={preacher.id} preacher={preacher} />
        ))}
      </View>
    </ScrollView>
  );
};
