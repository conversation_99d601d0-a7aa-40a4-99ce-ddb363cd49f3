1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ballayoussouf.alfajr"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:6:3-75
11-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:2:3-64
12-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
13-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:3:3-77
13-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:3:20-75
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:4:3-77
14-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:4:20-75
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:5:3-68
15-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:5:20-66
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:7:3-63
16-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:8:3-78
17-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:8:20-76
18
19    <queries>
19-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:9:3-15:13
20        <intent>
20-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:7-58
21-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:7-67
23-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:7-37
25-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:13-35
26        </intent>
27
28        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
28-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
28-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
29        <intent>
29-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
30            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
30-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
31        </intent>
32
33        <package android:name="androidx.test.orchestrator" />
33-->[androidx.test:runner:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e00c40288a49a4adb3216a36a69a2ef6\transformed\runner-1.4.0\AndroidManifest.xml:25:9-62
33-->[androidx.test:runner:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e00c40288a49a4adb3216a36a69a2ef6\transformed\runner-1.4.0\AndroidManifest.xml:25:18-59
34        <package android:name="androidx.test.services" />
34-->[androidx.test:runner:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e00c40288a49a4adb3216a36a69a2ef6\transformed\runner-1.4.0\AndroidManifest.xml:26:9-58
34-->[androidx.test:runner:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e00c40288a49a4adb3216a36a69a2ef6\transformed\runner-1.4.0\AndroidManifest.xml:26:18-55
35        <package android:name="com.google.android.apps.common.testing.services" />
35-->[androidx.test:runner:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e00c40288a49a4adb3216a36a69a2ef6\transformed\runner-1.4.0\AndroidManifest.xml:27:9-83
35-->[androidx.test:runner:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e00c40288a49a4adb3216a36a69a2ef6\transformed\runner-1.4.0\AndroidManifest.xml:27:18-80
36    </queries>
37
38    <uses-permission android:name="android.permission.WAKE_LOCK" />
38-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-68
38-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-65
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
39-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-77
39-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-74
40    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
40-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-92
40-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-89
41    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
41-->[com.google.android.exoplayer:exoplayer-core:2.19.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2eacfe8e3674b51a5e18112d2898ac\transformed\exoplayer-core-2.19.0\AndroidManifest.xml:24:5-79
41-->[com.google.android.exoplayer:exoplayer-core:2.19.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b2eacfe8e3674b51a5e18112d2898ac\transformed\exoplayer-core-2.19.0\AndroidManifest.xml:24:22-76
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.ballayoussouf.alfajr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.ballayoussouf.alfajr.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
49-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:3-32:17
50        android:name="com.ballayoussouf.alfajr.MainApplication"
50-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:16-47
51        android:allowBackup="true"
51-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:162-188
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2eafbfcd37e5312020438d485ffc72b9\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:icon="@mipmap/ic_launcher"
55-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:81-115
56        android:label="@string/app_name"
56-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:48-80
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:116-161
58        android:supportsRtl="true"
58-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:221-247
59        android:theme="@style/AppTheme"
59-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:16:189-220
60        android:usesCleartextTraffic="true" >
60-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\debug\AndroidManifest.xml:6:18-53
61        <meta-data
61-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:17:5-83
62            android:name="expo.modules.updates.ENABLED"
62-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:17:16-59
63            android:value="false" />
63-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:17:60-81
64        <meta-data
64-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:18:5-105
65            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
65-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:18:16-80
66            android:value="ALWAYS" />
66-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:18:81-103
67        <meta-data
67-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:19:5-99
68            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
68-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:19:16-79
69            android:value="0" />
69-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:19:80-97
70
71        <activity
71-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:5-31:16
72            android:name="com.ballayoussouf.alfajr.MainActivity"
72-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:15-43
73            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
73-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:44-134
74            android:exported="true"
74-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:256-279
75            android:launchMode="singleTask"
75-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:135-166
76            android:screenOrientation="portrait"
76-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:280-316
77            android:theme="@style/Theme.App.SplashScreen"
77-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:210-255
78            android:windowSoftInputMode="adjustResize" >
78-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:20:167-209
79            <intent-filter>
79-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:21:7-24:23
80                <action android:name="android.intent.action.MAIN" />
80-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:22:9-60
80-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:22:17-58
81
82                <category android:name="android.intent.category.LAUNCHER" />
82-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:23:9-68
82-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:23:19-66
83            </intent-filter>
84            <intent-filter>
84-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:25:7-30:23
85                <action android:name="android.intent.action.VIEW" />
85-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:7-58
85-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:15-56
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:27:9-67
87-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:27:19-65
88                <category android:name="android.intent.category.BROWSABLE" />
88-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:7-67
88-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:17-65
89
90                <data android:scheme="exp+al-fajr" />
90-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:7-37
90-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:13-35
91            </intent-filter>
92        </activity> <!-- The main service, handles playback, playlists and media buttons -->
93        <service
93-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-22:19
94            android:name="com.doublesymmetry.trackplayer.service.MusicService"
94-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-79
95            android:enabled="true"
95-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-35
96            android:exported="true"
96-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-36
97            android:foregroundServiceType="mediaPlayback" >
97-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-58
98            <intent-filter>
98-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-21:29
99                <action android:name="android.intent.action.MEDIA_BUTTON" />
99-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-77
99-->[:react-native-track-player] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:25-74
100            </intent-filter>
101        </service>
102
103        <activity
103-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
104            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
104-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
105            android:exported="true"
105-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
106            android:launchMode="singleTask"
106-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
107            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
107-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
108            <intent-filter>
108-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
109                <action android:name="android.intent.action.VIEW" />
109-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:7-58
109-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:15-56
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:27:9-67
111-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:27:19-65
112                <category android:name="android.intent.category.BROWSABLE" />
112-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:7-67
112-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:17-65
113
114                <data android:scheme="expo-dev-launcher" />
114-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:7-37
114-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:13-35
115            </intent-filter>
116        </activity>
117        <activity
117-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
118            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
118-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
119            android:screenOrientation="portrait"
119-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
120            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
120-->[:expo-dev-launcher] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
121        <activity
121-->[:expo-dev-menu] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
122            android:name="expo.modules.devmenu.DevMenuActivity"
122-->[:expo-dev-menu] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
123            android:exported="true"
123-->[:expo-dev-menu] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
124            android:launchMode="singleTask"
124-->[:expo-dev-menu] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
125            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
125-->[:expo-dev-menu] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
126            <intent-filter>
126-->[:expo-dev-menu] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
127                <action android:name="android.intent.action.VIEW" />
127-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:7-58
127-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:11:15-56
128
129                <category android:name="android.intent.category.DEFAULT" />
129-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:27:9-67
129-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:27:19-65
130                <category android:name="android.intent.category.BROWSABLE" />
130-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:7-67
130-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:12:17-65
131
132                <data android:scheme="expo-dev-menu" />
132-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:7-37
132-->D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\AndroidManifest.xml:13:13-35
133            </intent-filter>
134        </activity>
135
136        <meta-data
136-->[:expo-modules-core] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
137            android:name="org.unimodules.core.AppLoader#react-native-headless"
137-->[:expo-modules-core] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
138            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
138-->[:expo-modules-core] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
139        <meta-data
139-->[:expo-modules-core] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
140            android:name="com.facebook.soloader.enabled"
140-->[:expo-modules-core] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
141            android:value="true" />
141-->[:expo-modules-core] D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
142
143        <activity
143-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
144            android:name="com.facebook.react.devsupport.DevSettingsActivity"
144-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
145            android:exported="false" />
145-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d644ee9b842757375fb9cebdd915ee04\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
146
147        <provider
147-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
148            android:name="expo.modules.filesystem.FileSystemFileProvider"
148-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
149            android:authorities="com.ballayoussouf.alfajr.FileSystemFileProvider"
149-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
150            android:exported="false"
150-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
151            android:grantUriPermissions="true" >
151-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
152            <meta-data
152-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:27:13-29:70
153                android:name="android.support.FILE_PROVIDER_PATHS"
153-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:28:17-67
154                android:resource="@xml/file_system_provider_paths" />
154-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b4cf25cb545af971136b0f929fcdac0\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:29:17-67
155        </provider>
156        <provider
156-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
157            android:name="androidx.startup.InitializationProvider"
157-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
158            android:authorities="com.ballayoussouf.alfajr.androidx-startup"
158-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
159            android:exported="false" >
159-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
160            <meta-data
160-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
161                android:name="androidx.emoji2.text.EmojiCompatInitializer"
161-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
162                android:value="androidx.startup" />
162-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6a06dd8db20fe48f1d4dcd15ee18fe12\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
163            <meta-data
163-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
164                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
164-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
165                android:value="androidx.startup" />
165-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b59c4c4f9e32b429eb79d019301b76c8\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
166            <meta-data
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
168                android:value="androidx.startup" />
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
169        </provider>
170
171        <receiver
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
172            android:name="androidx.profileinstaller.ProfileInstallReceiver"
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
173            android:directBootAware="false"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
174            android:enabled="true"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
175            android:exported="true"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
176            android:permission="android.permission.DUMP" >
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
177            <intent-filter>
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
178                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
179            </intent-filter>
180            <intent-filter>
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
181                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
182            </intent-filter>
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
184                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
187                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\00803497dde122c5c17921be9ade43a6\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
188            </intent-filter>
189        </receiver>
190    </application>
191
192</manifest>
