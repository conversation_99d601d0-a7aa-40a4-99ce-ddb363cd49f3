import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { useAudioPlayer } from '../contexts/AudioPlayerContext';
import { useTheme } from '../contexts/ThemeContext';
import { formatTime, getDefaultImage } from '../utils/helpers';
import { Ionicons } from '@expo/vector-icons';

interface MiniPlayerProps {
  onPress?: () => void;
}

export const MiniPlayer: React.FC<MiniPlayerProps> = ({ onPress }) => {
  const { isDarkMode } = useTheme();
  const { currentAudio, isPlaying, progress, duration, pauseAudio, resumeAudio } = useAudioPlayer();

  if (!currentAudio) return null;

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseAudio();
    } else {
      resumeAudio();
    }
  };

  const progressPercentage = duration > 0 ? (progress / duration) * 100 : 0;

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`absolute bottom-0 left-0 right-0 p-3 ${
        isDarkMode ? 'bg-dark-800' : 'bg-white'
      } border-t border-gray-200`}
      style={{ elevation: 8 }}
    >
      <View className="flex-row items-center">
        <Image
          source={{ uri: currentAudio.cover_url || getDefaultImage('audio') }}
          className="w-12 h-12 rounded-lg mr-3"
          resizeMode="cover"
        />
        
        <View className="flex-1">
          <Text
            className={`font-semibold text-sm ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}
            numberOfLines={1}
          >
            {currentAudio.title}
          </Text>
          
          <Text
            className={`text-xs ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}
          >
            {currentAudio.preacher?.name || 'Prédicateur inconnu'}
          </Text>
        </View>
        
        <TouchableOpacity
          onPress={handlePlayPause}
          className="w-10 h-10 rounded-full bg-primary-500 justify-center items-center mr-3"
        >
          <Ionicons
            name={isPlaying ? 'pause' : 'play'}
            size={20}
            color="white"
          />
        </TouchableOpacity>
      </View>
      
      {/* Barre de progression */}
      <View className="h-1 bg-gray-200 rounded-full mt-2">
        <View
          className="h-1 bg-primary-500 rounded-full"
          style={{ width: `${progressPercentage}%` }}
        />
      </View>
    </TouchableOpacity>
  );
};
