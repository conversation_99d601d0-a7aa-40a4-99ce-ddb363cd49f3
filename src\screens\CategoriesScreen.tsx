import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { categoryService } from '../services/supabase';
import { Category } from '../types';
import { getCategoryColor } from '../utils/helpers';

export const CategoriesScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      const data = await categoryService.getAll();
      setCategories(data);
    } catch (error) {
      console.error('Erreur lors du chargement des catégories:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <View className={`flex-1 justify-center items-center ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}>
        <Text className={`text-base ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Chargement...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      className={`flex-1 ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View className="p-4">
        <Text className={`text-xl font-bold mb-6 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Catégories
        </Text>
        
        <View className="flex-row flex-wrap justify-between">
          {categories.map((category) => (
            <TouchableOpacity
              key={category.id}
              className={`w-[48%] p-4 rounded-lg mb-4 ${
                isDarkMode ? 'bg-dark-800' : 'bg-white'
              } shadow-sm`}
              style={{ elevation: 2 }}
            >
              <View className={`w-12 h-12 rounded-full mb-3 ${getCategoryColor(category.name)} justify-center items-center`}>
                <Text className="text-white font-bold text-base">
                  {category.name.charAt(0)}
                </Text>
              </View>
              
              <Text className={`font-semibold text-base mb-2 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                {category.name}
              </Text>
              
              {category.description && (
                <Text className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  {category.description}
                </Text>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </ScrollView>
  );
};
