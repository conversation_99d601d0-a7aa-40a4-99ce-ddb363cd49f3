{"logs": [{"outputFile": "com.ballayoussouf.alfajr.app-mergeDebugResources-54:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b11e6b985dd17975fd05953637789a7\\transformed\\exoplayer-ui-2.19.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,480,674,760,847,930,1018,1102,1170,1234,1332,1430,1495,1563,1629,1702,1822,1942,2060,2135,2217,2293,2361,2451,2542,2607,2671,2724,2784,2832,2893,2957,3028,3092,3157,3222,3281,3346,3410,3476,3528,3588,3671,3754", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "280,475,669,755,842,925,1013,1097,1165,1229,1327,1425,1490,1558,1624,1697,1817,1937,2055,2130,2212,2288,2356,2446,2537,2602,2666,2719,2779,2827,2888,2952,3023,3087,3152,3217,3276,3341,3405,3471,3523,3583,3666,3749,3801"}, "to": {"startLines": "2,11,15,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,575,5465,5551,5638,5721,5809,5893,5961,6025,6123,6221,6286,6354,6420,6493,6613,6733,6851,6926,7008,7084,7152,7242,7333,7398,8145,8439,8499,8547,8608,8672,8743,8807,8872,8937,8996,9061,9125,9191,9243,9303,9386,9469", "endLines": "10,14,18,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,85,86,82,87,83,67,63,97,97,64,67,65,72,119,119,117,74,81,75,67,89,90,64,63,52,59,47,60,63,70,63,64,64,58,64,63,65,51,59,82,82,51", "endOffsets": "375,570,764,5546,5633,5716,5804,5888,5956,6020,6118,6216,6281,6349,6415,6488,6608,6728,6846,6921,7003,7079,7147,7237,7328,7393,7457,8193,8494,8542,8603,8667,8738,8802,8867,8932,8991,9056,9120,9186,9238,9298,9381,9464,9516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,606,719,796,871,964,1059,1154,1248,1350,1445,1542,1640,1736,1829,1909,2015,2114,2210,2315,2418,2520,2674,2776", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "203,306,417,501,601,714,791,866,959,1054,1149,1243,1345,1440,1537,1635,1731,1824,1904,2010,2109,2205,2310,2413,2515,2669,2771,2851"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "935,1038,1141,1252,1336,1436,1549,1626,1701,1794,1889,1984,2078,2180,2275,2372,2470,2566,2659,2739,2845,2944,3040,3145,3248,3350,3504,15454", "endColumns": "102,102,110,83,99,112,76,74,92,94,94,93,101,94,96,97,95,92,79,105,98,95,104,102,101,153,101,79", "endOffsets": "1033,1136,1247,1331,1431,1544,1621,1696,1789,1884,1979,2073,2175,2270,2367,2465,2561,2654,2734,2840,2939,3035,3140,3243,3345,3499,3601,15529"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,365,457,538,640,720,818,940,1019,1078,1141,1233,1297,1357,1449,1514,1577,1639,1706,1770,1824,1929,1988,2049,2103,2172,2291,2374,2451,2541,2625,2709,2845,2924,3008,3130,3216,3294,3348,3399,3465,3534,3608,3679,3755,3827,3904,3975,4049,4160,4251,4330,4417,4505,4577,4651,4736,4787,4866,4933,5014,5098,5160,5224,5287,5355,5462,5561,5660,5755,5813,5868,5946,6027,6106", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "261,360,452,533,635,715,813,935,1014,1073,1136,1228,1292,1352,1444,1509,1572,1634,1701,1765,1819,1924,1983,2044,2098,2167,2286,2369,2446,2536,2620,2704,2840,2919,3003,3125,3211,3289,3343,3394,3460,3529,3603,3674,3750,3822,3899,3970,4044,4155,4246,4325,4412,4500,4572,4646,4731,4782,4861,4928,5009,5093,5155,5219,5282,5350,5457,5556,5655,5750,5808,5863,5941,6022,6101,6189"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,3679,3778,3870,3951,4053,4861,4959,5081,5343,5402,9521,9995,10208,10335,10427,10492,10555,10617,10684,10748,10802,10907,10966,11027,11081,11150,11487,11570,11647,11737,11821,11905,12041,12120,12204,12326,12412,12490,12544,12595,12661,12730,12804,12875,12951,13023,13100,13171,13245,13356,13447,13526,13613,13701,13773,13847,13932,13983,14062,14129,14210,14294,14356,14420,14483,14551,14658,14757,14856,14951,15009,15064,15534,15615,15694", "endLines": "22,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "endColumns": "12,98,91,80,101,79,97,121,78,58,62,91,63,59,91,64,62,61,66,63,53,104,58,60,53,68,118,82,76,89,83,83,135,78,83,121,85,77,53,50,65,68,73,70,75,71,76,70,73,110,90,78,86,87,71,73,84,50,78,66,80,83,61,63,62,67,106,98,98,94,57,54,77,80,78,87", "endOffsets": "930,3773,3865,3946,4048,4128,4954,5076,5155,5397,5460,9608,10054,10263,10422,10487,10550,10612,10679,10743,10797,10902,10961,11022,11076,11145,11264,11565,11642,11732,11816,11900,12036,12115,12199,12321,12407,12485,12539,12590,12656,12725,12799,12870,12946,13018,13095,13166,13240,13351,13442,13521,13608,13696,13768,13842,13927,13978,14057,14124,14205,14289,14351,14415,14478,14546,14653,14752,14851,14946,15004,15059,15137,15610,15689,15777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,282,350,431,498,565,639,716,798,877,946,1028,1111,1188,1271,1350,1427,1497,1566,1651,1731,1806", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "123,206,277,345,426,493,560,634,711,793,872,941,1023,1106,1183,1266,1345,1422,1492,1561,1646,1726,1801,1879"}, "to": {"startLines": "50,66,128,130,131,133,147,148,149,196,197,198,199,204,205,206,207,208,209,210,211,213,214,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3606,5160,9924,10059,10127,10268,11269,11336,11410,15142,15224,15303,15372,15782,15865,15942,16025,16104,16181,16251,16320,16506,16586,16661", "endColumns": "72,82,70,67,80,66,66,73,76,81,78,68,81,82,76,82,78,76,69,68,84,79,74,77", "endOffsets": "3674,5238,9990,10122,10203,10330,11331,11405,11482,15219,15298,15367,15449,15860,15937,16020,16099,16176,16246,16315,16400,16581,16656,16734"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "56,57,58,59,60,61,62,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4133,4228,4330,4428,4527,4635,4740,16405", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "4223,4325,4423,4522,4630,4735,4856,16501"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "67,125,126,127", "startColumns": "4,4,4,4", "startOffsets": "5243,9613,9713,9826", "endColumns": "99,99,112,97", "endOffsets": "5338,9708,9821,9919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b2eacfe8e3674b51a5e18112d2898ac\\transformed\\exoplayer-core-2.19.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,191,255,330,411,485,579,665", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "123,186,250,325,406,480,574,660,733"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7462,7535,7598,7662,7737,7818,7892,7986,8072", "endColumns": "72,62,63,74,80,73,93,85,72", "endOffsets": "7530,7593,7657,7732,7813,7887,7981,8067,8140"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd4652fb8811503ef112af2338db59dd\\transformed\\extension-mediasession-2.19.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,217", "endColumns": "80,80,78", "endOffsets": "131,212,291"}, "to": {"startLines": "104,105,106", "startColumns": "4,4,4", "startOffsets": "8198,8279,8360", "endColumns": "80,80,78", "endOffsets": "8274,8355,8434"}}]}]}