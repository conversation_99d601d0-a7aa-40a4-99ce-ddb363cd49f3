{"logs": [{"outputFile": "com.ballayoussouf.alfajr.app-mergeDebugResources-54:/values-ja/values-ja.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b11e6b985dd17975fd05953637789a7\\transformed\\exoplayer-ui-2.19.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,281,444,604,676,747,816,895,973,1039,1100,1178,1255,1319,1380,1439,1504,1591,1678,1766,1831,1897,1962,2026,2107,2187,2248,2311,2363,2421,2469,2530,2586,2648,2705,2765,2821,2877,2940,3002,3065,3115,3173,3245,3317", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "276,439,599,671,742,811,890,968,1034,1095,1173,1250,1314,1375,1434,1499,1586,1673,1761,1826,1892,1957,2021,2102,2182,2243,2306,2358,2416,2464,2525,2581,2643,2700,2760,2816,2872,2935,2997,3060,3110,3168,3240,3312,3361"}, "to": {"startLines": "2,11,15,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,376,539,5086,5158,5229,5298,5377,5455,5521,5582,5660,5737,5801,5862,5921,5986,6073,6160,6248,6313,6379,6444,6508,6589,6669,6730,7400,7680,7738,7786,7847,7903,7965,8022,8082,8138,8194,8257,8319,8382,8432,8490,8562,8634", "endLines": "10,14,18,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,71,70,68,78,77,65,60,77,76,63,60,58,64,86,86,87,64,65,64,63,80,79,60,62,51,57,47,60,55,61,56,59,55,55,62,61,62,49,57,71,71,48", "endOffsets": "371,534,694,5153,5224,5293,5372,5450,5516,5577,5655,5732,5796,5857,5916,5981,6068,6155,6243,6308,6374,6439,6503,6584,6664,6725,6788,7447,7733,7781,7842,7898,7960,8017,8077,8133,8189,8252,8314,8377,8427,8485,8557,8629,8678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd4652fb8811503ef112af2338db59dd\\transformed\\extension-mediasession-2.19.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,131,206", "endColumns": "75,74,76", "endOffsets": "126,201,278"}, "to": {"startLines": "104,105,106", "startColumns": "4,4,4", "startOffsets": "7452,7528,7603", "endColumns": "75,74,76", "endOffsets": "7523,7598,7675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,197,265,331,407,473,540,612,687,763,839,906,981,1056,1128,1205,1281,1353,1423,1492,1570,1638,1709,1777", "endColumns": "67,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "118,192,260,326,402,468,535,607,682,758,834,901,976,1051,1123,1200,1276,1348,1418,1487,1565,1633,1704,1772,1843"}, "to": {"startLines": "50,66,128,130,131,133,147,148,149,196,197,198,199,204,205,206,207,208,209,210,211,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3438,4803,9058,9190,9256,9390,10313,10380,10452,13848,13924,14000,14067,14443,14518,14590,14667,14743,14815,14885,14954,15133,15201,15272,15340", "endColumns": "67,73,67,65,75,65,66,71,74,75,75,66,74,74,71,76,75,71,69,68,77,67,70,67,70", "endOffsets": "3501,4872,9121,9251,9327,9451,10375,10447,10522,13919,13995,14062,14137,14513,14585,14662,14738,14810,14880,14949,15027,15196,15267,15335,15406"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b2eacfe8e3674b51a5e18112d2898ac\\transformed\\exoplayer-core-2.19.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,180,245,307,381,440,520,597", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "115,175,240,302,376,435,515,592,657"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6793,6858,6918,6983,7045,7119,7178,7258,7335", "endColumns": "64,59,64,61,73,58,79,76,64", "endOffsets": "6853,6913,6978,7040,7114,7173,7253,7330,7395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "67,125,126,127", "startColumns": "4,4,4,4", "startOffsets": "4877,8767,8862,8963", "endColumns": "92,94,100,94", "endOffsets": "4965,8857,8958,9053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,253,320,384,453,534,616,701,805,881,934,997,1081,1145,1203,1284,1345,1409,1464,1523,1580,1634,1727,1783,1840,1894,1960,2060,2136,2207,2286,2359,2440,2562,2624,2686,2787,2866,2941,2994,3045,3111,3181,3251,3322,3392,3456,3527,3595,3658,3749,3828,3891,3971,4053,4125,4196,4268,4316,4388,4452,4527,4604,4666,4730,4793,4860,4946,5032,5113,5196,5253,5308,5381,5459,5532", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "248,315,379,448,529,611,696,800,876,929,992,1076,1140,1198,1279,1340,1404,1459,1518,1575,1629,1722,1778,1835,1889,1955,2055,2131,2202,2281,2354,2435,2557,2619,2681,2782,2861,2936,2989,3040,3106,3176,3246,3317,3387,3451,3522,3590,3653,3744,3823,3886,3966,4048,4120,4191,4263,4311,4383,4447,4522,4599,4661,4725,4788,4855,4941,5027,5108,5191,5248,5303,5376,5454,5527,5598"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "699,3506,3573,3637,3706,3787,4538,4623,4727,4970,5023,8683,9126,9332,9456,9537,9598,9662,9717,9776,9833,9887,9980,10036,10093,10147,10213,10527,10603,10674,10753,10826,10907,11029,11091,11153,11254,11333,11408,11461,11512,11578,11648,11718,11789,11859,11923,11994,12062,12125,12216,12295,12358,12438,12520,12592,12663,12735,12783,12855,12919,12994,13071,13133,13197,13260,13327,13413,13499,13580,13663,13720,13775,14221,14299,14372", "endLines": "22,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "endColumns": "12,66,63,68,80,81,84,103,75,52,62,83,63,57,80,60,63,54,58,56,53,92,55,56,53,65,99,75,70,78,72,80,121,61,61,100,78,74,52,50,65,69,69,70,69,63,70,67,62,90,78,62,79,81,71,70,71,47,71,63,74,76,61,63,62,66,85,85,80,82,56,54,72,77,72,70", "endOffsets": "847,3568,3632,3701,3782,3864,4618,4722,4798,5018,5081,8762,9185,9385,9532,9593,9657,9712,9771,9828,9882,9975,10031,10088,10142,10208,10308,10598,10669,10748,10821,10902,11024,11086,11148,11249,11328,11403,11456,11507,11573,11643,11713,11784,11854,11918,11989,12057,12120,12211,12290,12353,12433,12515,12587,12658,12730,12778,12850,12914,12989,13066,13128,13192,13255,13322,13408,13494,13575,13658,13715,13770,13843,14294,14367,14438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,295,400,482,580,688,766,841,932,1025,1120,1214,1314,1407,1502,1596,1687,1778,1856,1958,2056,2151,2254,2350,2446,2594,2691", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "197,290,395,477,575,683,761,836,927,1020,1115,1209,1309,1402,1497,1591,1682,1773,1851,1953,2051,2146,2249,2345,2441,2589,2686,2765"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "852,949,1042,1147,1229,1327,1435,1513,1588,1679,1772,1867,1961,2061,2154,2249,2343,2434,2525,2603,2705,2803,2898,3001,3097,3193,3341,14142", "endColumns": "96,92,104,81,97,107,77,74,90,92,94,93,99,92,94,93,90,90,77,101,97,94,102,95,95,147,96,78", "endOffsets": "944,1037,1142,1224,1322,1430,1508,1583,1674,1767,1862,1956,2056,2149,2244,2338,2429,2520,2598,2700,2798,2893,2996,3092,3188,3336,3433,14216"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "56,57,58,59,60,61,62,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3869,3961,4061,4155,4251,4344,4437,15032", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "3956,4056,4150,4246,4339,4432,4533,15128"}}]}]}