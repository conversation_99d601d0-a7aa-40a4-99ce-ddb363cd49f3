if(NOT TARGET react-native-reanimated::reanimated)
add_library(react-native-reanimated::reanimated SHARED IMPORTED)
set_target_properties(react-native-reanimated::reanimated PROPERTIES
    IMPORTED_LOCATION "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/y1v3r242/obj/arm64-v8a/libreanimated.so"
    INTERFACE_INCLUDE_DIRECTORIES "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

