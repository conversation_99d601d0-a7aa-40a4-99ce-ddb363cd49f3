{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-5513170d084e50317899.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al <PERSON>ajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [3]}, {"build": "RNCSlider_autolinked_build", "jsonFile": "directory-RNCSlider_autolinked_build-Debug-1a7e2772c00f4016cf3f.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/@react-native-community/slider/android/src/main/jni", "targetIndexes": [1]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-e95db5e77c3c06eadd19.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al F<PERSON>r/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-7a595206335d0534467e.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al <PERSON>r/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-deeb9820de3352338239.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al <PERSON>r/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [5]}, {"build": "RNVectorIconsSpec_autolinked_build", "jsonFile": "directory-RNVectorIconsSpec_autolinked_build-Debug-1a86575275f5ebe814d4.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rnworklets_autolinked_build", "jsonFile": "directory-rnworklets_autolinked_build-Debug-dafa179d060eedab06ad.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "D:/Al Fajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native-worklets/android/build/generated/source/codegen/jni", "targetIndexes": [6]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-be2246cefbf8fe9a8ee7.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNCSlider::@4898bc4726ecf1751b6a", "jsonFile": "target-react_codegen_RNCSlider-Debug-58cfa34ccbf9d1577d94.json", "name": "react_codegen_RNCSlider", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d", "jsonFile": "target-react_codegen_RNVectorIconsSpec-Debug-9ba774ff11e1e5f18f3f.json", "name": "react_codegen_RNVectorIconsSpec", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-2d8cf202663bfeba8c89.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-33c1beabc3f484db3462.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-6d904d264b0926140fa3.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnworklets::@68f58d84d4754f193387", "jsonFile": "target-react_codegen_rnworklets-Debug-852bf92e1f3856ac3bd3.json", "name": "react_codegen_rnworklets", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-79b67e1c44ec2d7f5b80.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/Al <PERSON>/Version/Alpha/al-fajr_alpha/al-fajr/android/app/.cxx/Debug/q4e5d6l3/arm64-v8a", "source": "D:/Al <PERSON>ajr/Version/Alpha/al-fajr_alpha/al-fajr/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}