{"installationFolder": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "4.1.0", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\y1v3r242\\obj\\arm64-v8a\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\y1v3r242\\arm64-v8a\\android_gradle_build.json"}]}]}}