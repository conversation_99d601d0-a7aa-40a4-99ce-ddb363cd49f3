-- Script de configuration de la base de données Al Fajr
-- À exécuter dans l'éditeur SQL de Supabase

-- Table des prédicateurs
CREATE TABLE preachers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  bio TEXT,
  country TEXT DEFAULT 'Cameroun',
  photo_url TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Table des catégories
CREATE TABLE categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL UNIQUE,
  description TEXT
);

-- Table des audios
CREATE TABLE audios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  preacher_id UUID REFERENCES preachers(id) ON DELETE CASCADE,
  category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  audio_url TEXT NOT NULL,
  duration INTEGER,
  cover_url TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Table des playlists
CREATE TABLE playlists (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  preacher_id UUID REFERENCES preachers(id) ON DELETE SET NULL,
  cover_url TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Table de relation playlist <-> audios
CREATE TABLE playlist_audios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  playlist_id UUID REFERENCES playlists(id) ON DELETE CASCADE,
  audio_id UUID REFERENCES audios(id) ON DELETE CASCADE,
  position INTEGER,
  UNIQUE(playlist_id, audio_id)
);

-- Données d'exemple

-- Catégories
INSERT INTO categories (name, description) VALUES
('Tafsir', 'Exégèse et interprétation du Coran'),
('Fiqh', 'Jurisprudence islamique'),
('Aqida', 'Croyance et théologie'),
('Hadith', 'Traditions prophétiques'),
('Sira', 'Biographie du Prophète (SAW)'),
('Adab', 'Éthique et bonnes manières');

-- Prédicateurs d'exemple
INSERT INTO preachers (name, bio, country) VALUES
('Cheikh Ahmad Bamba', 'Érudit sénégalais, fondateur du mouridisme', 'Sénégal'),
('Cheikh Uthman Dan Fodio', 'Réformateur et érudit du 19ème siècle', 'Nigeria'),
('Cheikh Hassan Al-Banna', 'Fondateur des Frères Musulmans', 'Égypte'),
('Cheikh Muhammad Al-Ghazali', 'Érudit contemporain égyptien', 'Égypte'),
('Cheikh Abdur-Rahman As-Saadi', 'Érudit saoudien du 20ème siècle', 'Arabie Saoudite');

-- Audios d'exemple (URLs à remplacer par de vrais fichiers)
INSERT INTO audios (preacher_id, category_id, title, description, audio_url, duration, cover_url) VALUES
((SELECT id FROM preachers WHERE name = 'Cheikh Ahmad Bamba' LIMIT 1), 
 (SELECT id FROM categories WHERE name = 'Tafsir' LIMIT 1),
 'Tafsir de la Fatiha', 'Exégèse détaillée de la première sourate', 
 'https://example.com/audio1.mp3', 3600, 'https://example.com/cover1.jpg'),

((SELECT id FROM preachers WHERE name = 'Cheikh Uthman Dan Fodio' LIMIT 1),
 (SELECT id FROM categories WHERE name = 'Fiqh' LIMIT 1),
 'Les fondements du Fiqh', 'Cours sur les bases de la jurisprudence',
 'https://example.com/audio2.mp3', 2700, 'https://example.com/cover2.jpg'),

((SELECT id FROM preachers WHERE name = 'Cheikh Hassan Al-Banna' LIMIT 1),
 (SELECT id FROM categories WHERE name = 'Aqida' LIMIT 1),
 'Les piliers de la foi', 'Explication des fondements de la croyance',
 'https://example.com/audio3.mp3', 1800, 'https://example.com/cover3.jpg');

-- Playlists d'exemple
INSERT INTO playlists (name, description, preacher_id) VALUES
('Tafsir du Coran', 'Collection complète de tafsir', 
 (SELECT id FROM preachers WHERE name = 'Cheikh Ahmad Bamba' LIMIT 1)),
('Cours de Fiqh', 'Série sur la jurisprudence islamique',
 (SELECT id FROM preachers WHERE name = 'Cheikh Uthman Dan Fodio' LIMIT 1)),
('Aqida pour débutants', 'Introduction à la croyance islamique',
 (SELECT id FROM preachers WHERE name = 'Cheikh Hassan Al-Banna' LIMIT 1));

-- Relations playlist-audio
INSERT INTO playlist_audios (playlist_id, audio_id, position) VALUES
((SELECT id FROM playlists WHERE name = 'Tafsir du Coran' LIMIT 1),
 (SELECT id FROM audios WHERE title = 'Tafsir de la Fatiha' LIMIT 1), 1),
((SELECT id FROM playlists WHERE name = 'Cours de Fiqh' LIMIT 1),
 (SELECT id FROM audios WHERE title = 'Les fondements du Fiqh' LIMIT 1), 1),
((SELECT id FROM playlists WHERE name = 'Aqida pour débutants' LIMIT 1),
 (SELECT id FROM audios WHERE title = 'Les piliers de la foi' LIMIT 1), 1);

-- Index pour les performances
CREATE INDEX idx_audios_preacher ON audios(preacher_id);
CREATE INDEX idx_audios_category ON audios(category_id);
CREATE INDEX idx_audios_created_at ON audios(created_at DESC);
CREATE INDEX idx_playlist_audios_playlist ON playlist_audios(playlist_id);
CREATE INDEX idx_playlist_audios_position ON playlist_audios(position);

-- Politiques RLS (Row Level Security) pour l'accès public en lecture
ALTER TABLE preachers ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE audios ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE playlist_audios ENABLE ROW LEVEL SECURITY;

-- Politiques pour permettre la lecture publique
CREATE POLICY "Allow public read access on preachers" ON preachers
  FOR SELECT USING (true);

CREATE POLICY "Allow public read access on categories" ON categories
  FOR SELECT USING (true);

CREATE POLICY "Allow public read access on audios" ON audios
  FOR SELECT USING (true);

CREATE POLICY "Allow public read access on playlists" ON playlists
  FOR SELECT USING (true);

CREATE POLICY "Allow public read access on playlist_audios" ON playlist_audios
  FOR SELECT USING (true);

-- Fonctions utilitaires
CREATE OR REPLACE FUNCTION get_audio_with_details(audio_id UUID)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  audio_url TEXT,
  duration INTEGER,
  cover_url TEXT,
  created_at TIMESTAMP,
  preacher_name TEXT,
  preacher_photo TEXT,
  category_name TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.title,
    a.description,
    a.audio_url,
    a.duration,
    a.cover_url,
    a.created_at,
    p.name as preacher_name,
    p.photo_url as preacher_photo,
    c.name as category_name
  FROM audios a
  LEFT JOIN preachers p ON a.preacher_id = p.id
  LEFT JOIN categories c ON a.category_id = c.id
  WHERE a.id = audio_id;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour obtenir les audios d'une playlist
CREATE OR REPLACE FUNCTION get_playlist_audios(playlist_id UUID)
RETURNS TABLE (
  audio_id UUID,
  position INTEGER,
  title TEXT,
  preacher_name TEXT,
  duration INTEGER,
  cover_url TEXT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id as audio_id,
    pa.position,
    a.title,
    p.name as preacher_name,
    a.duration,
    a.cover_url
  FROM playlist_audios pa
  JOIN audios a ON pa.audio_id = a.id
  LEFT JOIN preachers p ON a.preacher_id = p.id
  WHERE pa.playlist_id = playlist_id
  ORDER BY pa.position;
END;
$$ LANGUAGE plpgsql;
