import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Audio } from '../types';
import { useAudioPlayer } from '../contexts/AudioPlayerContext';
import { useTheme } from '../contexts/ThemeContext';
import { formatTime, getDefaultImage, truncateText } from '../utils/helpers';
import { Ionicons } from '@expo/vector-icons';

interface AudioCardProps {
  audio: Audio;
  onPress?: () => void;
}

export const AudioCard: React.FC<AudioCardProps> = ({ audio, onPress }) => {
  const { isDarkMode } = useTheme();
  const { currentAudio, isPlaying, playAudio, pauseAudio, resumeAudio } = useAudioPlayer();
  
  const isCurrentAudio = currentAudio?.id === audio.id;
  const isCurrentlyPlaying = isCurrentAudio && isPlaying;

  const handlePlayPress = () => {
    if (isCurrentAudio) {
      if (isPlaying) {
        pauseAudio();
      } else {
        resumeAudio();
      }
    } else {
      playAudio(audio);
    }
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`p-3 rounded-lg mb-3 ${
        isDarkMode ? 'bg-dark-800' : 'bg-white'
      } shadow-sm`}
      style={{ elevation: 2 }}
    >
      <View className="flex-row items-center">
        <TouchableOpacity
          onPress={handlePlayPress}
          className="mr-3"
        >
          <Image
            source={{ uri: audio.cover_url || getDefaultImage('audio') }}
            className="w-16 h-16 rounded-lg"
            resizeMode="cover"
          />
          <View className="absolute inset-0 bg-black bg-opacity-30 rounded-lg justify-center items-center">
            <Ionicons
              name={isCurrentlyPlaying ? 'pause' : 'play'}
              size={20}
              color="white"
            />
          </View>
        </TouchableOpacity>
        
        <View className="flex-1">
          <Text
            className={`font-semibold text-sm mb-1 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}
            numberOfLines={2}
          >
            {truncateText(audio.title, 50)}
          </Text>
          
          <Text
            className={`text-xs mb-1 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}
          >
            {audio.preacher?.name || 'Prédicateur inconnu'}
          </Text>
          
          {audio.category && (
            <View className="flex-row items-center mb-1">
              <View className="bg-primary-500 px-2 py-1 rounded-full mr-2">
                <Text className="text-white text-xs font-medium">
                  {audio.category.name}
                </Text>
              </View>
            </View>
          )}
          
          {audio.duration && (
            <Text
              className={`text-xs ${
                isDarkMode ? 'text-gray-400' : 'text-gray-500'
              }`}
            >
              {formatTime(audio.duration)}
            </Text>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};
