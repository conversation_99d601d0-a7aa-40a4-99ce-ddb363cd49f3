{"logs": [{"outputFile": "com.ballayoussouf.alfajr.app-mergeDebugResources-54:/values-fi/values-fi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,208,280,348,431,500,570,649,728,813,899,973,1055,1139,1215,1300,1384,1464,1543,1618,1703,1779,1859,1930", "endColumns": "70,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "121,203,275,343,426,495,565,644,723,808,894,968,1050,1134,1210,1295,1379,1459,1538,1613,1698,1774,1854,1925,2004"}, "to": {"startLines": "50,66,128,130,131,133,147,148,149,196,197,198,199,204,205,206,207,208,209,210,211,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3625,5122,9833,9980,10048,10196,11210,11280,11359,15146,15231,15317,15391,15794,15878,15954,16039,16123,16203,16282,16357,16543,16619,16699,16770", "endColumns": "70,81,71,67,82,68,69,78,78,84,85,73,81,83,75,84,83,79,78,74,84,75,79,70,78", "endOffsets": "3691,5199,9900,10043,10126,10260,11275,11354,11433,15226,15312,15386,15468,15873,15949,16034,16118,16198,16277,16352,16437,16614,16694,16765,16844"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "56,57,58,59,60,61,62,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4100,4196,4298,4396,4501,4606,4718,16442", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "4191,4293,4391,4496,4601,4713,4829,16538"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,344,418,501,590,672,768,876,960,1022,1087,1180,1255,1320,1408,1473,1539,1597,1668,1734,1788,1898,1958,2022,2076,2149,2265,2349,2425,2516,2597,2678,2811,2896,2981,3114,3204,3278,3330,3381,3447,3524,3606,3677,3751,3825,3904,3981,4053,4160,4249,4325,4416,4511,4585,4658,4752,4806,4880,4952,5038,5124,5186,5250,5313,5384,5485,5588,5683,5783,5839,5894,5973,6059,6138", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "263,339,413,496,585,667,763,871,955,1017,1082,1175,1250,1315,1403,1468,1534,1592,1663,1729,1783,1893,1953,2017,2071,2144,2260,2344,2420,2511,2592,2673,2806,2891,2976,3109,3199,3273,3325,3376,3442,3519,3601,3672,3746,3820,3899,3976,4048,4155,4244,4320,4411,4506,4580,4653,4747,4801,4875,4947,5033,5119,5181,5245,5308,5379,5480,5583,5678,5778,5834,5889,5968,6054,6133,6208"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,3696,3772,3846,3929,4018,4834,4930,5038,5307,5369,9431,9905,10131,10265,10353,10418,10484,10542,10613,10679,10733,10843,10903,10967,11021,11094,11438,11522,11598,11689,11770,11851,11984,12069,12154,12287,12377,12451,12503,12554,12620,12697,12779,12850,12924,12998,13077,13154,13226,13333,13422,13498,13589,13684,13758,13831,13925,13979,14053,14125,14211,14297,14359,14423,14486,14557,14658,14761,14856,14956,15012,15067,15554,15640,15719", "endLines": "22,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "endColumns": "12,75,73,82,88,81,95,107,83,61,64,92,74,64,87,64,65,57,70,65,53,109,59,63,53,72,115,83,75,90,80,80,132,84,84,132,89,73,51,50,65,76,81,70,73,73,78,76,71,106,88,75,90,94,73,72,93,53,73,71,85,85,61,63,62,70,100,102,94,99,55,54,78,85,78,74", "endOffsets": "950,3767,3841,3924,4013,4095,4925,5033,5117,5364,5429,9519,9975,10191,10348,10413,10479,10537,10608,10674,10728,10838,10898,10962,11016,11089,11205,11517,11593,11684,11765,11846,11979,12064,12149,12282,12372,12446,12498,12549,12615,12692,12774,12845,12919,12993,13072,13149,13221,13328,13417,13493,13584,13679,13753,13826,13920,13974,14048,14120,14206,14292,14354,14418,14481,14552,14653,14756,14851,14951,15007,15062,15141,15635,15714,15789"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "67,125,126,127", "startColumns": "4,4,4,4", "startOffsets": "5204,9524,9625,9734", "endColumns": "102,100,108,98", "endOffsets": "5302,9620,9729,9828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b2eacfe8e3674b51a5e18112d2898ac\\transformed\\exoplayer-core-2.19.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,120,179,241,308,385,455,549,641", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "115,174,236,303,380,450,544,636,707"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7364,7429,7488,7550,7617,7694,7764,7858,7950", "endColumns": "64,58,61,66,76,69,93,91,70", "endOffsets": "7424,7483,7545,7612,7689,7759,7853,7945,8016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd4652fb8811503ef112af2338db59dd\\transformed\\extension-mediasession-2.19.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,147,235", "endColumns": "91,87,89", "endOffsets": "142,230,320"}, "to": {"startLines": "104,105,106", "startColumns": "4,4,4", "startOffsets": "8074,8166,8254", "endColumns": "91,87,89", "endOffsets": "8161,8249,8339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b11e6b985dd17975fd05953637789a7\\transformed\\exoplayer-ui-2.19.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,491,692,783,876,957,1053,1145,1216,1283,1372,1459,1527,1592,1655,1727,1820,1910,2001,2078,2160,2232,2303,2397,2493,2558,2622,2675,2733,2781,2842,2910,2982,3051,3123,3190,3245,3310,3376,3442,3494,3555,3630,3705", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,92,89,90,76,81,71,70,93,95,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "282,486,687,778,871,952,1048,1140,1211,1278,1367,1454,1522,1587,1650,1722,1815,1905,1996,2073,2155,2227,2298,2392,2488,2553,2617,2670,2728,2776,2837,2905,2977,3046,3118,3185,3240,3305,3371,3437,3489,3550,3625,3700,3757"}, "to": {"startLines": "2,11,15,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,586,5434,5525,5618,5699,5795,5887,5958,6025,6114,6201,6269,6334,6397,6469,6562,6652,6743,6820,6902,6974,7045,7139,7235,7300,8021,8344,8402,8450,8511,8579,8651,8720,8792,8859,8914,8979,9045,9111,9163,9224,9299,9374", "endLines": "10,14,18,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,90,92,80,95,91,70,66,88,86,67,64,62,71,92,89,90,76,81,71,70,93,95,64,63,52,57,47,60,67,71,68,71,66,54,64,65,65,51,60,74,74,56", "endOffsets": "377,581,782,5520,5613,5694,5790,5882,5953,6020,6109,6196,6264,6329,6392,6464,6557,6647,6738,6815,6897,6969,7040,7134,7230,7295,7359,8069,8397,8445,8506,8574,8646,8715,8787,8854,8909,8974,9040,9106,9158,9219,9294,9369,9426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,422,508,613,731,817,896,987,1080,1175,1269,1363,1456,1552,1651,1742,1836,1916,2023,2124,2221,2327,2427,2525,2675,2775", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "208,308,417,503,608,726,812,891,982,1075,1170,1264,1358,1451,1547,1646,1737,1831,1911,2018,2119,2216,2322,2422,2520,2670,2770,2851"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "955,1063,1163,1272,1358,1463,1581,1667,1746,1837,1930,2025,2119,2213,2306,2402,2501,2592,2686,2766,2873,2974,3071,3177,3277,3375,3525,15473", "endColumns": "107,99,108,85,104,117,85,78,90,92,94,93,93,92,95,98,90,93,79,106,100,96,105,99,97,149,99,80", "endOffsets": "1058,1158,1267,1353,1458,1576,1662,1741,1832,1925,2020,2114,2208,2301,2397,2496,2587,2681,2761,2868,2969,3066,3172,3272,3370,3520,3620,15549"}}]}]}