{"buildFiles": ["D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\@react-native-community\\slider\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"react_codegen_RNCSlider::@4898bc4726ecf1751b6a": {"artifactName": "react_codegen_RNCSlider", "abi": "arm64-v8a", "output": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\intermediates\\cxx\\Debug\\q4e5d6l3\\obj\\arm64-v8a\\libreact_codegen_RNCSlider.so", "runtimeFiles": []}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"artifactName": "react_codegen_RNVectorIconsSpec", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a": {"artifactName": "react_codegen_rnreanimated", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"artifactName": "react_codegen_safeareacontext", "abi": "arm64-v8a", "output": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\intermediates\\cxx\\Debug\\q4e5d6l3\\obj\\arm64-v8a\\libreact_codegen_safeareacontext.so", "runtimeFiles": []}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"artifactName": "react_codegen_rnscreens", "abi": "arm64-v8a", "output": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\intermediates\\cxx\\Debug\\q4e5d6l3\\obj\\arm64-v8a\\libreact_codegen_rnscreens.so", "runtimeFiles": []}, "appmodules::@6890427a1f51a3e7e1df": {"artifactName": "appmodules", "abi": "arm64-v8a", "output": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\build\\intermediates\\cxx\\Debug\\q4e5d6l3\\obj\\arm64-v8a\\libappmodules.so", "runtimeFiles": []}, "react_codegen_rnworklets::@68f58d84d4754f193387": {"artifactName": "react_codegen_rnworklets", "abi": "arm64-v8a", "runtimeFiles": []}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"artifactName": "react_codegen_rnasyncstorage", "abi": "arm64-v8a", "runtimeFiles": []}}}