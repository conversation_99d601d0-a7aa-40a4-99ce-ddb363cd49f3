<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-updates-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-json-utils\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-manifests\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-client\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="dev-menu-packager-host" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\dev-menu-packager-host"/><file name="EXDevMenuApp.android.js" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\EXDevMenuApp.android.js"/><file name="Inter-Black.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="expo_dev_launcher_android.bundle" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\expo_dev_launcher_android.bundle"/><file name="Inter-Black.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":react-native-worklets" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-worklets\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-vector-icons" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-vector-icons\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_slider" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\@react-native-community\slider\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-track-player" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-track-player\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Al Fajr\Version\Alpha\al-fajr_alpha\al-fajr\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>