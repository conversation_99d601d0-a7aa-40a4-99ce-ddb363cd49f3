{"logs": [{"outputFile": "com.ballayoussouf.alfajr.app-mergeDebugResources-54:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,432,519,623,739,822,900,991,1084,1179,1273,1373,1466,1561,1655,1746,1837,1923,2026,2131,2232,2336,2445,2553,2713,2812", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "215,319,427,514,618,734,817,895,986,1079,1174,1268,1368,1461,1556,1650,1741,1832,1918,2021,2126,2227,2331,2440,2548,2708,2807,2892"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "910,1025,1129,1237,1324,1428,1544,1627,1705,1796,1889,1984,2078,2178,2271,2366,2460,2551,2642,2728,2831,2936,3037,3141,3250,3358,3518,14482", "endColumns": "114,103,107,86,103,115,82,77,90,92,94,93,99,92,94,93,90,90,85,102,104,100,103,108,107,159,98,84", "endOffsets": "1020,1124,1232,1319,1423,1539,1622,1700,1791,1884,1979,2073,2173,2266,2361,2455,2546,2637,2723,2826,2931,3032,3136,3245,3353,3513,3612,14562"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b2eacfe8e3674b51a5e18112d2898ac\\transformed\\exoplayer-core-2.19.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,185,250,313,389,453,553,647", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "118,180,245,308,384,448,548,642,711"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7264,7332,7394,7459,7522,7598,7662,7762,7856", "endColumns": "67,61,64,62,75,63,99,93,68", "endOffsets": "7327,7389,7454,7517,7593,7657,7757,7851,7920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "65,123,124,125", "startColumns": "4,4,4,4", "startOffsets": "5061,9383,9481,9590", "endColumns": "99,97,108,100", "endOffsets": "5156,9476,9585,9686"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd4652fb8811503ef112af2338db59dd\\transformed\\extension-mediasession-2.19.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,218", "endColumns": "80,81,76", "endOffsets": "131,213,290"}, "to": {"startLines": "102,103,104", "startColumns": "4,4,4", "startOffsets": "7978,8059,8141", "endColumns": "80,81,76", "endOffsets": "8054,8136,8213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "55,56,57,58,59,60,61,191", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4026,4121,4223,4320,4417,4523,4641,14804", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "4116,4218,4315,4412,4518,4636,4751,14900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,348,424,503,593,678,784,900,983,1047,1112,1206,1271,1330,1417,1479,1541,1601,1667,1729,1783,1895,1952,2013,2067,2139,2265,2351,2429,2522,2608,2692,2831,2912,2993,3128,3218,3300,3353,3405,3471,3543,3627,3698,3778,3853,3929,4002,4077,4175,4260,4335,4427,4521,4595,4668,4762,4814,4896,4965,5050,5137,5199,5263,5326,5398,5501,5606,5701,5804,5861,5917,5997,6078,6156", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "264,343,419,498,588,673,779,895,978,1042,1107,1201,1266,1325,1412,1474,1536,1596,1662,1724,1778,1890,1947,2008,2062,2134,2260,2346,2424,2517,2603,2687,2826,2907,2988,3123,3213,3295,3348,3400,3466,3538,3622,3693,3773,3848,3924,3997,4072,4170,4255,4330,4422,4516,4590,4663,4757,4809,4891,4960,5045,5132,5194,5258,5321,5393,5496,5601,5696,5799,5856,5912,5992,6073,6151,6229"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,67,122,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "741,3617,3696,3772,3851,3941,4756,4862,4978,5161,5225,9289,9691,9756,9815,9902,9964,10026,10086,10152,10214,10268,10380,10437,10498,10552,10624,10750,10836,10914,11007,11093,11177,11316,11397,11478,11613,11703,11785,11838,11890,11956,12028,12112,12183,12263,12338,12414,12487,12562,12660,12745,12820,12912,13006,13080,13153,13247,13299,13381,13450,13535,13622,13684,13748,13811,13883,13986,14091,14186,14289,14346,14402,14567,14648,14726", "endLines": "22,50,51,52,53,54,62,63,64,66,67,122,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,188,189,190", "endColumns": "12,78,75,78,89,84,105,115,82,63,64,93,64,58,86,61,61,59,65,61,53,111,56,60,53,71,125,85,77,92,85,83,138,80,80,134,89,81,52,51,65,71,83,70,79,74,75,72,74,97,84,74,91,93,73,72,93,51,81,68,84,86,61,63,62,71,102,104,94,102,56,55,79,80,77,77", "endOffsets": "905,3691,3767,3846,3936,4021,4857,4973,5056,5220,5285,9378,9751,9810,9897,9959,10021,10081,10147,10209,10263,10375,10432,10493,10547,10619,10745,10831,10909,11002,11088,11172,11311,11392,11473,11608,11698,11780,11833,11885,11951,12023,12107,12178,12258,12333,12409,12482,12557,12655,12740,12815,12907,13001,13075,13148,13242,13294,13376,13445,13530,13617,13679,13743,13806,13878,13981,14086,14181,14284,14341,14397,14477,14643,14721,14799"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b11e6b985dd17975fd05953637789a7\\transformed\\exoplayer-ui-2.19.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,472,646,729,814,890,978,1071,1148,1217,1313,1407,1471,1535,1601,1674,1789,1907,2023,2095,2175,2245,2319,2403,2489,2556,2620,2673,2731,2779,2840,2904,2966,3029,3095,3157,3220,3286,3350,3416,3468,3530,3606,3682", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "280,467,641,724,809,885,973,1066,1143,1212,1308,1402,1466,1530,1596,1669,1784,1902,2018,2090,2170,2240,2314,2398,2484,2551,2615,2668,2726,2774,2835,2899,2961,3024,3090,3152,3215,3281,3345,3411,3463,3525,3601,3677,3739"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,567,5290,5373,5458,5534,5622,5715,5792,5861,5957,6051,6115,6179,6245,6318,6433,6551,6667,6739,6819,6889,6963,7047,7133,7200,7925,8218,8276,8324,8385,8449,8511,8574,8640,8702,8765,8831,8895,8961,9013,9075,9151,9227", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,82,84,75,87,92,76,68,95,93,63,63,65,72,114,117,115,71,79,69,73,83,85,66,63,52,57,47,60,63,61,62,65,61,62,65,63,65,51,61,75,75,61", "endOffsets": "375,562,736,5368,5453,5529,5617,5710,5787,5856,5952,6046,6110,6174,6240,6313,6428,6546,6662,6734,6814,6884,6958,7042,7128,7195,7259,7973,8271,8319,8380,8444,8506,8569,8635,8697,8760,8826,8890,8956,9008,9070,9146,9222,9284"}}]}]}