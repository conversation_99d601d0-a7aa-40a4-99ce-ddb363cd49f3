import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Preacher } from '../types';
import { useTheme } from '../contexts/ThemeContext';
import { getDefaultImage, truncateText } from '../utils/helpers';

interface PreacherCardProps {
  preacher: Preacher;
  onPress?: () => void;
}

export const PreacherCard: React.FC<PreacherCardProps> = ({ preacher, onPress }) => {
  const { isDarkMode } = useTheme();

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`p-4 rounded-lg mb-3 ${
        isDarkMode ? 'bg-dark-800' : 'bg-white'
      } shadow-sm`}
      style={{ elevation: 2 }}
    >
      <View className="flex-row items-center">
        <Image
          source={{ uri: preacher.photo_url || getDefaultImage('preacher') }}
          className="w-16 h-16 rounded-full mr-4"
          resizeMode="cover"
        />
        
        <View className="flex-1">
          <Text
            className={`font-semibold text-base mb-1 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}
          >
            {preacher.name}
          </Text>
          
          {preacher.bio && (
            <Text
              className={`text-sm mb-2 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}
              numberOfLines={2}
            >
              {truncateText(preacher.bio, 80)}
            </Text>
          )}
          
          <View className="flex-row items-center">
            <View className="bg-primary-100 px-2 py-1 rounded-full">
              <Text className="text-primary-700 text-xs font-medium">
                {preacher.country}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );
};
