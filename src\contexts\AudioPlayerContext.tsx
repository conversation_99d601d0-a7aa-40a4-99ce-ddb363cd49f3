import React, { createContext, useContext, useState, useRef, useEffect } from 'react';
import { Audio } from 'expo-av';
import { Audio as AudioType, PlayerState } from '../types';

interface AudioPlayerContextType extends PlayerState {
  playAudio: (audio: AudioType, playlist?: AudioType[]) => Promise<void>;
  pauseAudio: () => void;
  resumeAudio: () => void;
  stopAudio: () => void;
  seekTo: (position: number) => void;
  playNext: () => void;
  playPrevious: () => void;
  setPlaylist: (playlist: AudioType[]) => void;
}

const AudioPlayerContext = createContext<AudioPlayerContextType | undefined>(undefined);

interface AudioPlayerProviderProps {
  children: React.ReactNode;
}

export const useAudioPlayer = () => {
  const context = useContext(AudioPlayerContext);
  if (context === undefined) {
    throw new Error('useAudioPlayer must be used within an AudioPlayerProvider');
  }
  return context;
};

export const AudioPlayerProvider: React.FC<AudioPlayerProviderProps> = ({ children }) => {
  const [currentAudio, setCurrentAudio] = useState<AudioType | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [duration, setDuration] = useState(0);
  const [currentPlaylist, setCurrentPlaylist] = useState<AudioType[] | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const soundRef = useRef<Audio.Sound | null>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    return () => {
      if (soundRef.current) {
        soundRef.current.unloadAsync();
      }
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  const startProgressTracking = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
    }
    
    progressIntervalRef.current = setInterval(async () => {
      if (soundRef.current && isPlaying) {
        try {
          const status = await soundRef.current.getStatusAsync();
          if (status.isLoaded) {
            setProgress(status.positionMillis / 1000);
          }
        } catch (error) {
          console.error('Erreur lors du suivi de la progression:', error);
        }
      }
    }, 1000);
  };

  const stopProgressTracking = () => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
  };

  const playAudio = async (audio: AudioType, playlist?: AudioType[]) => {
    try {
      setIsLoading(true);
      
      // Arrêter l'audio actuel
      if (soundRef.current) {
        await soundRef.current.unloadAsync();
      }
      
      // Charger le nouvel audio
      const { sound } = await Audio.Sound.createAsync(
        { uri: audio.audio_url },
        { shouldPlay: true }
      );
      
      soundRef.current = sound;
      setCurrentAudio(audio);
      setIsPlaying(true);
      setIsLoading(false);
      
      if (playlist) {
        setCurrentPlaylist(playlist);
        setCurrentIndex(playlist.findIndex(item => item.id === audio.id));
      }
      
      // Obtenir la durée
      const status = await sound.getStatusAsync();
      if (status.isLoaded) {
        setDuration(status.durationMillis ? status.durationMillis / 1000 : 0);
      }
      
      startProgressTracking();
      
      // Écouter la fin de l'audio
      sound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded && status.didJustFinish) {
          playNext();
        }
      });
      
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
      setIsLoading(false);
    }
  };

  const pauseAudio = async () => {
    if (soundRef.current) {
      await soundRef.current.pauseAsync();
      setIsPlaying(false);
      stopProgressTracking();
    }
  };

  const resumeAudio = async () => {
    if (soundRef.current) {
      await soundRef.current.playAsync();
      setIsPlaying(true);
      startProgressTracking();
    }
  };

  const stopAudio = async () => {
    if (soundRef.current) {
      await soundRef.current.unloadAsync();
      soundRef.current = null;
    }
    setCurrentAudio(null);
    setIsPlaying(false);
    setIsLoading(false);
    setProgress(0);
    setDuration(0);
    stopProgressTracking();
  };

  const seekTo = async (position: number) => {
    if (soundRef.current) {
      await soundRef.current.setPositionAsync(position * 1000);
      setProgress(position);
    }
  };

  const playNext = () => {
    if (currentPlaylist && currentIndex < currentPlaylist.length - 1) {
      const nextIndex = currentIndex + 1;
      const nextAudio = currentPlaylist[nextIndex];
      setCurrentIndex(nextIndex);
      playAudio(nextAudio, currentPlaylist);
    }
  };

  const playPrevious = () => {
    if (currentPlaylist && currentIndex > 0) {
      const prevIndex = currentIndex - 1;
      const prevAudio = currentPlaylist[prevIndex];
      setCurrentIndex(prevIndex);
      playAudio(prevAudio, currentPlaylist);
    }
  };

  const setPlaylist = (playlist: AudioType[]) => {
    setCurrentPlaylist(playlist);
  };

  return (
    <AudioPlayerContext.Provider
      value={{
        currentAudio,
        isPlaying,
        isLoading,
        progress,
        duration,
        currentPlaylist,
        currentIndex,
        playAudio,
        pauseAudio,
        resumeAudio,
        stopAudio,
        seekTo,
        playNext,
        playPrevious,
        setPlaylist,
      }}
    >
      {children}
    </AudioPlayerContext.Provider>
  );
};
