[{"level_": 0, "message_": "Start JSON generation. Platform version: 24 min SDK version: arm64-v8a", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "rebuilding JSON D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a\\android_gradle_build.json due to:", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "- no fingerprint file, will remove stale configuration folder", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Program Files\\\\Eclipse Adoptium\\\\jdk-*********-hotspot\\\\bin\\\\java\" ^\n  --class-path ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\modules-2\\\\files-2.1\\\\com.google.prefab\\\\cli\\\\2.1.0\\\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\\\cli-2.1.0-all.jar\" ^\n  com.google.prefab.cli.AppKt ^\n  --build-system ^\n  cmake ^\n  --platform ^\n  android ^\n  --abi ^\n  arm64-v8a ^\n  --os-version ^\n  24 ^\n  --stl ^\n  c++_shared ^\n  --ndk-version ^\n  27 ^\n  --output ^\n  \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\agp-prefab-staging17479148392582750292\\\\staged-cli-output\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\d644ee9b842757375fb9cebdd915ee04\\\\transformed\\\\react-android-0.79.5-debug\\\\prefab\" ^\n  \"D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-reanimated\\\\6a5x95s3\" ^\n  \"D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\refs\\\\react-native-worklets\\\\642b5m70\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\fa03d1ffcf2d88d54ed6c7d80f87d2da\\\\transformed\\\\hermes-android-0.79.5-debug\\\\prefab\" ^\n  \"C:\\\\Users\\\\<USER>\\\\.gradle\\\\caches\\\\8.13\\\\transforms\\\\e80cc6deab05b24bdfe1060903f43f89\\\\transformed\\\\fbjni-0.7.0\\\\prefab\"\n", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "removing stale contents from 'D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a'", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "created folder 'D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a'", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "executing cmake @echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\q4e5d6l3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\q4e5d6l3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\.cxx\\\\Debug\\\\q4e5d6l3\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BD:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\.cxx\\\\Debug\\\\q4e5d6l3\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "@echo off\n\"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\cmake.exe\" ^\n  \"-HD:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\node_modules\\\\react-native\\\\ReactAndroid\\\\cmake-utils\\\\default-app-setup\" ^\n  \"-DCMAKE_SYSTEM_NAME=Android\" ^\n  \"-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\" ^\n  \"-DCMAKE_SYSTEM_VERSION=24\" ^\n  \"-DANDROID_PLATFORM=android-24\" ^\n  \"-DANDROID_ABI=arm64-v8a\" ^\n  \"-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a\" ^\n  \"-DANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_ANDROID_NDK=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\" ^\n  \"-DCMAKE_TOOLCHAIN_FILE=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\ndk\\\\27.1.12297006\\\\build\\\\cmake\\\\android.toolchain.cmake\" ^\n  \"-DCMAKE_MAKE_PROGRAM=C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Android\\\\Sdk\\\\cmake\\\\3.22.1\\\\bin\\\\ninja.exe\" ^\n  \"-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\q4e5d6l3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\\\\intermediates\\\\cxx\\\\Debug\\\\q4e5d6l3\\\\obj\\\\arm64-v8a\" ^\n  \"-DCMAKE_BUILD_TYPE=Debug\" ^\n  \"-DCMAKE_FIND_ROOT_PATH=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\.cxx\\\\Debug\\\\q4e5d6l3\\\\prefab\\\\arm64-v8a\\\\prefab\" ^\n  \"-BD:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\.cxx\\\\Debug\\\\q4e5d6l3\\\\arm64-v8a\" ^\n  -GNinja ^\n  \"-DPROJECT_BUILD_DIR=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\\\\app\\\\build\" ^\n  \"-DPROJECT_ROOT_DIR=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\android\" ^\n  \"-DREACT_ANDROID_DIR=D:\\\\Al Fajr\\\\Version\\\\Alpha\\\\al-fajr_alpha\\\\al-fajr\\\\node_modules\\\\react-native\\\\ReactAndroid\" ^\n  \"-DANDROID_STL=c++_shared\" ^\n  \"-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON\"\n", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Received process result: 0", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "Exiting generation of D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a\\compile_commands.json.bin normally", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "done executing cmake", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "hard linked D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\Debug\\q4e5d6l3\\arm64-v8a\\compile_commands.json to D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\android\\app\\.cxx\\tools\\debug\\arm64-v8a\\compile_commands.json", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt", "tag_": "debug|arm64-v8a", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]