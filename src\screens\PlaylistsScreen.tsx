import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, RefreshControl } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { PlaylistCard } from '../components/PlaylistCard';
import { playlistService } from '../services/supabase';
import { Playlist } from '../types';

export const PlaylistsScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadPlaylists();
  }, []);

  const loadPlaylists = async () => {
    try {
      setLoading(true);
      const data = await playlistService.getAll();
      setPlaylists(data);
    } catch (error) {
      console.error('Erreur lors du chargement des playlists:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadPlaylists();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <View className={`flex-1 justify-center items-center ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}>
        <Text className={`text-base ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Chargement...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      className={`flex-1 ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View className="p-4">
        <Text className={`text-xl font-bold mb-6 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Playlists
        </Text>
        
        {playlists.map((playlist) => (
          <PlaylistCard key={playlist.id} playlist={playlist} />
        ))}
      </View>
    </ScrollView>
  );
};
