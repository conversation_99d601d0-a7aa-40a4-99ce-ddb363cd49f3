export interface Preacher {
  id: string;
  name: string;
  bio?: string;
  country: string;
  photo_url?: string;
  created_at: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
}

export interface Audio {
  id: string;
  preacher_id: string;
  category_id?: string;
  title: string;
  description?: string;
  audio_url: string;
  duration?: number;
  cover_url?: string;
  created_at: string;
  preacher?: Preacher;
  category?: Category;
}

export interface Playlist {
  id: string;
  name: string;
  description?: string;
  preacher_id?: string;
  cover_url?: string;
  created_at: string;
  preacher?: Preacher;
}

export interface PlaylistAudio {
  id: string;
  playlist_id: string;
  audio_id: string;
  position: number;
  audio?: Audio;
}

export interface PlayerState {
  currentAudio: Audio | null;
  isPlaying: boolean;
  isLoading: boolean;
  progress: number;
  duration: number;
  currentPlaylist: Audio[] | null;
  currentIndex: number;
}

export interface ThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
}
