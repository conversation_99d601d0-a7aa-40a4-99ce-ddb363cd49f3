import React from 'react';
import { View, Text, Switch, TouchableOpacity } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { Ionicons } from '@expo/vector-icons';

export const SettingsScreen: React.FC = () => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <View className={`flex-1 ${
      isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
    }`}>
      <View className="p-4">
        <Text className={`text-xl font-bold mb-6 ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Paramètres
        </Text>

        {/* Thème */}
        <View className={`p-4 rounded-lg mb-4 ${
          isDarkMode ? 'bg-dark-800' : 'bg-white'
        } shadow-sm`}>
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons
                name={isDarkMode ? 'moon' : 'sunny'}
                size={24}
                color={isDarkMode ? '#22c55e' : '#eab308'}
                style={{ marginRight: 12 }}
              />
              <View>
                <Text className={`font-semibold text-base ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Mode sombre
                </Text>
                <Text className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Activer le thème sombre
                </Text>
              </View>
            </View>
            <Switch
              value={isDarkMode}
              onValueChange={toggleTheme}
              trackColor={{ false: '#767577', true: '#22c55e' }}
              thumbColor={isDarkMode ? '#ffffff' : '#f4f3f4'}
            />
          </View>
        </View>

        {/* À propos */}
        <View className={`p-4 rounded-lg mb-4 ${
          isDarkMode ? 'bg-dark-800' : 'bg-white'
        } shadow-sm`}>
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons
                name="information-circle"
                size={24}
                color={isDarkMode ? '#22c55e' : '#eab308'}
                style={{ marginRight: 12 }}
              />
              <View>
                <Text className={`font-semibold text-base ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  À propos
                </Text>
                <Text className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Informations sur l'application
                </Text>
              </View>
            </View>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={isDarkMode ? '#6b7280' : '#9ca3af'}
            />
          </View>
        </View>

        {/* Version */}
        <View className={`p-4 rounded-lg ${
          isDarkMode ? 'bg-dark-800' : 'bg-white'
        } shadow-sm`}>
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center">
              <Ionicons
                name="logo-github"
                size={24}
                color={isDarkMode ? '#22c55e' : '#eab308'}
                style={{ marginRight: 12 }}
              />
              <View>
                <Text className={`font-semibold text-base ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Version
                </Text>
                <Text className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  Al Fajr v1.0.0
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};
