const fs = require('fs');
const path = require('path');

// Mappings des classes problématiques vers des classes sûres
const textSizeMappings = {
  'text-lg': 'text-base',
  'text-xl': 'text-lg',
  'text-2xl': 'text-xl',
  'text-3xl': 'text-2xl',
  'text-4xl': 'text-3xl',
  'text-5xl': 'text-4xl',
  'text-6xl': 'text-5xl',
  'text-7xl': 'text-6xl',
  'text-8xl': 'text-7xl',
  'text-9xl': 'text-8xl'
};

// Fonction pour traiter un fichier
function processFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Remplacer les classes problématiques
    for (const [oldClass, newClass] of Object.entries(textSizeMappings)) {
      if (content.includes(oldClass)) {
        content = content.replace(new RegExp(oldClass, 'g'), newClass);
        modified = true;
        console.log(`  ${oldClass} → ${newClass}`);
      }
    }
    
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Modifié: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Erreur avec ${filePath}:`, error.message);
  }
}

// Fonction pour parcourir récursivement les dossiers
function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !item.startsWith('node_modules') && !item.startsWith('.')) {
      processDirectory(fullPath);
    } else if (item.endsWith('.tsx') || item.endsWith('.ts')) {
      console.log(`\n📁 Traitement de: ${fullPath}`);
      processFile(fullPath);
    }
  }
}

console.log('🚀 Début de la correction des classes de taille de texte...\n');
processDirectory('./src');
console.log('\n✅ Correction terminée !');
