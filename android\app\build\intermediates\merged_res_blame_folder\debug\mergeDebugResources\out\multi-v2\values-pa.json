{"logs": [{"outputFile": "com.ballayoussouf.alfajr.app-mergeDebugResources-54:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b11e6b985dd17975fd05953637789a7\\transformed\\exoplayer-ui-2.19.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,284,487,667,756,843,926,1017,1111,1182,1245,1336,1427,1491,1554,1614,1682,1790,1907,2020,2090,2166,2237,2308,2394,2478,2544,2607,2660,2718,2766,2827,2887,2959,3021,3083,3144,3206,3271,3335,3401,3453,3513,3587,3661", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "279,482,662,751,838,921,1012,1106,1177,1240,1331,1422,1486,1549,1609,1677,1785,1902,2015,2085,2161,2232,2303,2389,2473,2539,2602,2655,2713,2761,2822,2882,2954,3016,3078,3139,3201,3266,3330,3396,3448,3508,3582,3656,3708"}, "to": {"startLines": "2,11,15,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,379,582,5421,5510,5597,5680,5771,5865,5936,5999,6090,6181,6245,6308,6368,6436,6544,6661,6774,6844,6920,6991,7062,7148,7232,7298,8061,8370,8428,8476,8537,8597,8669,8731,8793,8854,8916,8981,9045,9111,9163,9223,9297,9371", "endLines": "10,14,18,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,103,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "endColumns": "17,12,12,88,86,82,90,93,70,62,90,90,63,62,59,67,107,116,112,69,75,70,70,85,83,65,62,52,57,47,60,59,71,61,61,60,61,64,63,65,51,59,73,73,51", "endOffsets": "374,577,757,5505,5592,5675,5766,5860,5931,5994,6085,6176,6240,6303,6363,6431,6539,6656,6769,6839,6915,6986,7057,7143,7227,7293,7356,8109,8423,8471,8532,8592,8664,8726,8788,8849,8911,8976,9040,9106,9158,9218,9292,9366,9418"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b2eacfe8e3674b51a5e18112d2898ac\\transformed\\exoplayer-core-2.19.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,191,268,334,409,475,574,670", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "121,186,263,329,404,470,569,665,750"}, "to": {"startLines": "94,95,96,97,98,99,100,101,102", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7361,7432,7497,7574,7640,7715,7781,7880,7976", "endColumns": "70,64,76,65,74,65,98,95,84", "endOffsets": "7427,7492,7569,7635,7710,7776,7875,7971,8056"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd4652fb8811503ef112af2338db59dd\\transformed\\extension-mediasession-2.19.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,141,228", "endColumns": "85,86,82", "endOffsets": "136,223,306"}, "to": {"startLines": "104,105,106", "startColumns": "4,4,4", "startOffsets": "8114,8200,8287", "endColumns": "85,86,82", "endOffsets": "8195,8282,8365"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "67,125,126,127", "startColumns": "4,4,4,4", "startOffsets": "5196,9515,9616,9730", "endColumns": "104,100,113,102", "endOffsets": "5296,9611,9725,9828"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d644ee9b842757375fb9cebdd915ee04\\transformed\\react-android-0.79.5-debug\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,203,274,343,423,490,557,631,707,790,869,937,1015,1098,1172,1256,1344,1419,1490,1561,1647,1716,1790,1859", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,198,269,338,418,485,552,626,702,785,864,932,1010,1093,1167,1251,1339,1414,1485,1556,1642,1711,1785,1854,1927"}, "to": {"startLines": "50,66,128,130,131,133,147,148,149,196,197,198,199,204,205,206,207,208,209,210,211,213,214,215,216", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3587,5119,9833,9973,10042,10181,11178,11245,11319,15094,15177,15256,15324,15716,15799,15873,15957,16045,16120,16191,16262,16449,16518,16592,16661", "endColumns": "70,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "3653,5191,9899,10037,10117,10243,11240,11314,11390,15172,15251,15319,15397,15794,15868,15952,16040,16115,16186,16257,16343,16513,16587,16656,16729"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1047,1111,1203,1272,1331,1416,1479,1541,1599,1663,1724,1778,1892,1950,2010,2064,2134,2261,2342,2432,2531,2628,2707,2842,2918,2995,3124,3208,3290,3345,3400,3466,3535,3612,3683,3762,3830,3906,3976,4041,4143,4238,4311,4405,4498,4572,4641,4735,4791,4874,4941,5025,5113,5175,5239,5302,5369,5466,5572,5663,5765,5824,5883,5960,6045,6121", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "258,335,414,495,594,683,791,903,986,1042,1106,1198,1267,1326,1411,1474,1536,1594,1658,1719,1773,1887,1945,2005,2059,2129,2256,2337,2427,2526,2623,2702,2837,2913,2990,3119,3203,3285,3340,3395,3461,3530,3607,3678,3757,3825,3901,3971,4036,4138,4233,4306,4400,4493,4567,4636,4730,4786,4869,4936,5020,5108,5170,5234,5297,5364,5461,5567,5658,5760,5819,5878,5955,6040,6116,6189"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "762,3658,3735,3814,3895,3994,4816,4924,5036,5301,5357,9423,9904,10122,10248,10333,10396,10458,10516,10580,10641,10695,10809,10867,10927,10981,11051,11395,11476,11566,11665,11762,11841,11976,12052,12129,12258,12342,12424,12479,12534,12600,12669,12746,12817,12896,12964,13040,13110,13175,13277,13372,13445,13539,13632,13706,13775,13869,13925,14008,14075,14159,14247,14309,14373,14436,14503,14600,14706,14797,14899,14958,15017,15482,15567,15643", "endLines": "22,51,52,53,54,55,63,64,65,68,69,124,129,132,134,135,136,137,138,139,140,141,142,143,144,145,146,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,201,202,203", "endColumns": "12,76,78,80,98,88,107,111,82,55,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,89,98,96,78,134,75,76,128,83,81,54,54,65,68,76,70,78,67,75,69,64,101,94,72,93,92,73,68,93,55,82,66,83,87,61,63,62,66,96,105,90,101,58,58,76,84,75,72", "endOffsets": "920,3730,3809,3890,3989,4078,4919,5031,5114,5352,5416,9510,9968,10176,10328,10391,10453,10511,10575,10636,10690,10804,10862,10922,10976,11046,11173,11471,11561,11660,11757,11836,11971,12047,12124,12253,12337,12419,12474,12529,12595,12664,12741,12812,12891,12959,13035,13105,13170,13272,13367,13440,13534,13627,13701,13770,13864,13920,14003,14070,14154,14242,14304,14368,14431,14498,14595,14701,14792,14894,14953,15012,15089,15562,15638,15711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "56,57,58,59,60,61,62,212", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4083,4181,4283,4386,4487,4589,4687,16348", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "4176,4278,4381,4482,4584,4682,4811,16444"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "925,1028,1125,1230,1316,1416,1529,1607,1684,1775,1868,1962,2056,2156,2249,2344,2438,2529,2620,2699,2809,2912,3008,3119,3221,3331,3490,15402", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "1023,1120,1225,1311,1411,1524,1602,1679,1770,1863,1957,2051,2151,2244,2339,2433,2524,2615,2694,2804,2907,3003,3114,3216,3326,3485,3582,15477"}}]}]}