import React, { useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../contexts/ThemeContext';
import { useAudioPlayer } from '../contexts/AudioPlayerContext';

// Screens
import { HomeScreen } from '../screens/HomeScreen';
import { CategoriesScreen } from '../screens/CategoriesScreen';
import { PreachersScreen } from '../screens/PreachersScreen';
import { PlaylistsScreen } from '../screens/PlaylistsScreen';
import { SettingsScreen } from '../screens/SettingsScreen';

// Components
import { MiniPlayer } from '../components/MiniPlayer';
import { FullScreenPlayer } from '../components/FullScreenPlayer';
import { View } from 'react-native';

const Tab = createBottomTabNavigator();
const Stack = createNativeStackNavigator();

const PreachersStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="PreachersList" 
      component={PreachersScreen}
      options={{ title: 'Prédicateurs' }}
    />
  </Stack.Navigator>
);

const PlaylistsStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="PlaylistsList" 
      component={PlaylistsScreen}
      options={{ title: 'Playlists' }}
    />
  </Stack.Navigator>
);

const CategoriesStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="CategoriesList" 
      component={CategoriesScreen}
      options={{ title: 'Catégories' }}
    />
  </Stack.Navigator>
);

const SettingsStack = () => (
  <Stack.Navigator>
    <Stack.Screen 
      name="SettingsList" 
      component={SettingsScreen}
      options={{ title: 'Paramètres' }}
    />
  </Stack.Navigator>
);

const TabNavigator = () => {
  const { isDarkMode } = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === 'Accueil') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Catégories') {
            iconName = focused ? 'grid' : 'grid-outline';
          } else if (route.name === 'Prédicateurs') {
            iconName = focused ? 'people' : 'people-outline';
          } else if (route.name === 'Playlists') {
            iconName = focused ? 'list' : 'list-outline';
          } else if (route.name === 'Paramètres') {
            iconName = focused ? 'settings' : 'settings-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: isDarkMode ? '#22c55e' : '#eab308',
        tabBarInactiveTintColor: isDarkMode ? '#6b7280' : '#9ca3af',
        tabBarStyle: {
          backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
          borderTopColor: isDarkMode ? '#374151' : '#e5e7eb',
        },
        headerStyle: {
          backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
        },
        headerTintColor: isDarkMode ? '#ffffff' : '#000000',
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen 
        name="Accueil" 
        component={HomeScreen}
        options={{ headerShown: false }}
      />
      <Tab.Screen 
        name="Catégories" 
        component={CategoriesStack}
        options={{ headerShown: false }}
      />
      <Tab.Screen 
        name="Prédicateurs" 
        component={PreachersStack}
        options={{ headerShown: false }}
      />
      <Tab.Screen 
        name="Playlists" 
        component={PlaylistsStack}
        options={{ headerShown: false }}
      />
      <Tab.Screen 
        name="Paramètres" 
        component={SettingsStack}
        options={{ headerShown: false }}
      />
    </Tab.Navigator>
  );
};

export const AppNavigator = () => {
  const { currentAudio } = useAudioPlayer();
  const [showFullScreenPlayer, setShowFullScreenPlayer] = useState(false);

  return (
    <NavigationContainer>
      <View style={{ flex: 1 }}>
        <TabNavigator />
        {currentAudio && (
          <MiniPlayer onPress={() => setShowFullScreenPlayer(true)} />
        )}
        <FullScreenPlayer 
          visible={showFullScreenPlayer} 
          onClose={() => setShowFullScreenPlayer(false)} 
        />
      </View>
    </NavigationContainer>
  );
};
