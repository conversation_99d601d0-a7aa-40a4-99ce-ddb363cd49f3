  Application android.app  Build android.app.Activity  BuildConfig android.app.Activity  DefaultReactActivityDelegate android.app.Activity  R android.app.Activity  ReactActivityDelegateWrapper android.app.Activity  
fabricEnabled android.app.Activity  moveTaskToBack android.app.Activity  onCreate android.app.Activity  ApplicationLifecycleDispatcher android.app.Application  Boolean android.app.Application  BuildConfig android.app.Application  DefaultReactNativeHost android.app.Application  List android.app.Application  OpenSourceMergedSoMapping android.app.Application  PackageList android.app.Application  ReactNativeHostWrapper android.app.Application  ReactPackage android.app.Application  SoLoader android.app.Application  String android.app.Application  createReactHost android.app.Application  load android.app.Application  onApplicationCreate android.app.Application  onConfigurationChanged android.app.Application  onCreate android.app.Application  ApplicationLifecycleDispatcher android.content.Context  Boolean android.content.Context  Build android.content.Context  BuildConfig android.content.Context  DefaultReactActivityDelegate android.content.Context  DefaultReactNativeHost android.content.Context  List android.content.Context  OpenSourceMergedSoMapping android.content.Context  PackageList android.content.Context  R android.content.Context  ReactActivityDelegateWrapper android.content.Context  ReactNativeHostWrapper android.content.Context  ReactPackage android.content.Context  SoLoader android.content.Context  String android.content.Context  createReactHost android.content.Context  
fabricEnabled android.content.Context  load android.content.Context  onApplicationCreate android.content.Context  onConfigurationChanged android.content.Context  ApplicationLifecycleDispatcher android.content.ContextWrapper  Boolean android.content.ContextWrapper  Build android.content.ContextWrapper  BuildConfig android.content.ContextWrapper  DefaultReactActivityDelegate android.content.ContextWrapper  DefaultReactNativeHost android.content.ContextWrapper  List android.content.ContextWrapper  OpenSourceMergedSoMapping android.content.ContextWrapper  PackageList android.content.ContextWrapper  R android.content.ContextWrapper  ReactActivityDelegateWrapper android.content.ContextWrapper  ReactNativeHostWrapper android.content.ContextWrapper  ReactPackage android.content.ContextWrapper  SoLoader android.content.ContextWrapper  String android.content.ContextWrapper  applicationContext android.content.ContextWrapper  createReactHost android.content.ContextWrapper  
fabricEnabled android.content.ContextWrapper  load android.content.ContextWrapper  onApplicationCreate android.content.ContextWrapper  onConfigurationChanged android.content.ContextWrapper  
Configuration android.content.res  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  R android.os.Build.VERSION_CODES  Build  android.view.ContextThemeWrapper  BuildConfig  android.view.ContextThemeWrapper  DefaultReactActivityDelegate  android.view.ContextThemeWrapper  R  android.view.ContextThemeWrapper  ReactActivityDelegateWrapper  android.view.ContextThemeWrapper  
fabricEnabled  android.view.ContextThemeWrapper  setTheme  android.view.ContextThemeWrapper  Build #androidx.activity.ComponentActivity  BuildConfig #androidx.activity.ComponentActivity  DefaultReactActivityDelegate #androidx.activity.ComponentActivity  R #androidx.activity.ComponentActivity  ReactActivityDelegateWrapper #androidx.activity.ComponentActivity  
fabricEnabled #androidx.activity.ComponentActivity  Build (androidx.appcompat.app.AppCompatActivity  BuildConfig (androidx.appcompat.app.AppCompatActivity  DefaultReactActivityDelegate (androidx.appcompat.app.AppCompatActivity  R (androidx.appcompat.app.AppCompatActivity  ReactActivityDelegateWrapper (androidx.appcompat.app.AppCompatActivity  
fabricEnabled (androidx.appcompat.app.AppCompatActivity  setTheme (androidx.appcompat.app.AppCompatActivity  Build #androidx.core.app.ComponentActivity  BuildConfig #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  DefaultReactActivityDelegate #androidx.core.app.ComponentActivity  R #androidx.core.app.ComponentActivity  ReactActivityDelegate #androidx.core.app.ComponentActivity  ReactActivityDelegateWrapper #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
fabricEnabled #androidx.core.app.ComponentActivity  Build &androidx.fragment.app.FragmentActivity  BuildConfig &androidx.fragment.app.FragmentActivity  DefaultReactActivityDelegate &androidx.fragment.app.FragmentActivity  R &androidx.fragment.app.FragmentActivity  ReactActivityDelegateWrapper &androidx.fragment.app.FragmentActivity  
fabricEnabled &androidx.fragment.app.FragmentActivity  Application com.ballayoussouf.alfajr  ApplicationLifecycleDispatcher com.ballayoussouf.alfajr  Boolean com.ballayoussouf.alfajr  Build com.ballayoussouf.alfajr  BuildConfig com.ballayoussouf.alfajr  Bundle com.ballayoussouf.alfajr  
Configuration com.ballayoussouf.alfajr  DefaultReactActivityDelegate com.ballayoussouf.alfajr  DefaultReactNativeHost com.ballayoussouf.alfajr  List com.ballayoussouf.alfajr  MainActivity com.ballayoussouf.alfajr  MainApplication com.ballayoussouf.alfajr  OpenSourceMergedSoMapping com.ballayoussouf.alfajr  PackageList com.ballayoussouf.alfajr  R com.ballayoussouf.alfajr  
ReactActivity com.ballayoussouf.alfajr  ReactActivityDelegate com.ballayoussouf.alfajr  ReactActivityDelegateWrapper com.ballayoussouf.alfajr  ReactApplication com.ballayoussouf.alfajr  	ReactHost com.ballayoussouf.alfajr  ReactNativeHost com.ballayoussouf.alfajr  ReactNativeHostWrapper com.ballayoussouf.alfajr  ReactPackage com.ballayoussouf.alfajr  SoLoader com.ballayoussouf.alfajr  String com.ballayoussouf.alfajr  createReactHost com.ballayoussouf.alfajr  
fabricEnabled com.ballayoussouf.alfajr  load com.ballayoussouf.alfajr  onApplicationCreate com.ballayoussouf.alfajr  onConfigurationChanged com.ballayoussouf.alfajr  DEBUG $com.ballayoussouf.alfajr.BuildConfig  IS_HERMES_ENABLED $com.ballayoussouf.alfajr.BuildConfig  IS_NEW_ARCHITECTURE_ENABLED $com.ballayoussouf.alfajr.BuildConfig  Build %com.ballayoussouf.alfajr.MainActivity  BuildConfig %com.ballayoussouf.alfajr.MainActivity  R %com.ballayoussouf.alfajr.MainActivity  ReactActivityDelegateWrapper %com.ballayoussouf.alfajr.MainActivity  
fabricEnabled %com.ballayoussouf.alfajr.MainActivity  mainComponentName %com.ballayoussouf.alfajr.MainActivity  moveTaskToBack %com.ballayoussouf.alfajr.MainActivity  setTheme %com.ballayoussouf.alfajr.MainActivity  ApplicationLifecycleDispatcher (com.ballayoussouf.alfajr.MainApplication  BuildConfig (com.ballayoussouf.alfajr.MainApplication  OpenSourceMergedSoMapping (com.ballayoussouf.alfajr.MainApplication  PackageList (com.ballayoussouf.alfajr.MainApplication  ReactNativeHostWrapper (com.ballayoussouf.alfajr.MainApplication  SoLoader (com.ballayoussouf.alfajr.MainApplication  applicationContext (com.ballayoussouf.alfajr.MainApplication  createReactHost (com.ballayoussouf.alfajr.MainApplication  load (com.ballayoussouf.alfajr.MainApplication  onApplicationCreate (com.ballayoussouf.alfajr.MainApplication  onConfigurationChanged (com.ballayoussouf.alfajr.MainApplication  reactNativeHost (com.ballayoussouf.alfajr.MainApplication  AppTheme  com.ballayoussouf.alfajr.R.style  PackageList com.facebook.react  
ReactActivity com.facebook.react  ReactActivityDelegate com.facebook.react  ReactApplication com.facebook.react  	ReactHost com.facebook.react  ReactNativeHost com.facebook.react  ReactPackage com.facebook.react  packages com.facebook.react.PackageList  Build  com.facebook.react.ReactActivity  BuildConfig  com.facebook.react.ReactActivity  DefaultReactActivityDelegate  com.facebook.react.ReactActivity  R  com.facebook.react.ReactActivity  ReactActivityDelegateWrapper  com.facebook.react.ReactActivity  
fabricEnabled  com.facebook.react.ReactActivity  invokeDefaultOnBackPressed  com.facebook.react.ReactActivity  onCreate  com.facebook.react.ReactActivity  
fabricEnabled (com.facebook.react.ReactActivityDelegate  mainComponentName (com.facebook.react.ReactActivityDelegate  BuildConfig "com.facebook.react.ReactNativeHost  PackageList "com.facebook.react.ReactNativeHost  DefaultReactActivityDelegate com.facebook.react.defaults  DefaultReactNativeHost com.facebook.react.defaults  
fabricEnabled <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  load <com.facebook.react.defaults.DefaultNewArchitectureEntryPoint  OpenSourceMergedSoMapping com.facebook.react.soloader  SoLoader com.facebook.soloader  init com.facebook.soloader.SoLoader  ApplicationLifecycleDispatcher expo.modules  ReactActivityDelegateWrapper expo.modules  ReactNativeHostWrapper expo.modules  onApplicationCreate +expo.modules.ApplicationLifecycleDispatcher  onConfigurationChanged +expo.modules.ApplicationLifecycleDispatcher  	Companion #expo.modules.ReactNativeHostWrapper  createReactHost #expo.modules.ReactNativeHostWrapper  createReactHost -expo.modules.ReactNativeHostWrapper.Companion  Nothing kotlin  not kotlin.Boolean  	compareTo 
kotlin.Int  List kotlin.collections                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           