#!/bin/bash

# Script de configuration pour Al Fajr
echo "🚀 Configuration d'Al Fajr - Application Audio Islamique"
echo "=================================================="

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
    echo "❌ Node.js n'est pas installé. Veuillez l'installer depuis https://nodejs.org/"
    exit 1
fi

# Vérifier si npm est installé
if ! command -v npm &> /dev/null; then
    echo "❌ npm n'est pas installé. Veuillez l'installer."
    exit 1
fi

# Vérifier si Expo CLI est installé
if ! command -v expo &> /dev/null; then
    echo "📦 Installation d'Expo CLI..."
    npm install -g @expo/cli
fi

echo "📦 Installation des dépendances..."
npm install

echo "🔧 Configuration des variables d'environnement..."
if [ ! -f .env ]; then
    cat > .env << EOF
# Configuration Supabase
# Remplacez ces valeurs par vos vraies clés Supabase
EXPO_PUBLIC_SUPABASE_URL=VOTRE_URL_SUPABASE
EXPO_PUBLIC_SUPABASE_ANON_KEY=VOTRE_CLE_SUPABASE
EOF
    echo "✅ Fichier .env créé. Veuillez remplacer les valeurs par vos clés Supabase."
else
    echo "ℹ️  Le fichier .env existe déjà."
fi

echo ""
echo "🎉 Configuration terminée !"
echo ""
echo "📋 Prochaines étapes :"
echo "1. Créez un projet Supabase sur https://supabase.com"
echo "2. Exécutez le script SQL dans database/schema.sql"
echo "3. Remplacez les valeurs dans .env par vos clés Supabase"
echo "4. Lancez l'application avec : npm start"
echo ""
echo "📚 Documentation : README.md"
echo "🗄️  Script SQL : database/schema.sql"
echo ""
echo "🌟 Al Fajr - L'enseignement de nos prédécesseurs pieux"
