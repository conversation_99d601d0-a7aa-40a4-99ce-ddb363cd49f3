import React, { useState } from 'react';
import { View, Text, Image, TouchableOpacity, Modal, ScrollView } from 'react-native';
import { useAudioPlayer } from '../contexts/AudioPlayerContext';
import { useTheme } from '../contexts/ThemeContext';
import { formatTime, getDefaultImage } from '../utils/helpers';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';

interface FullScreenPlayerProps {
  visible: boolean;
  onClose: () => void;
}

export const FullScreenPlayer: React.FC<FullScreenPlayerProps> = ({ visible, onClose }) => {
  const { isDarkMode } = useTheme();
  const {
    currentAudio,
    isPlaying,
    isLoading,
    progress,
    duration,
    currentPlaylist,
    currentIndex,
    pauseAudio,
    resumeAudio,
    seekTo,
    playNext,
    playPrevious,
  } = useAudioPlayer();

  const [showPlaylist, setShowPlaylist] = useState(false);

  if (!currentAudio) return null;

  const handlePlayPause = () => {
    if (isPlaying) {
      pauseAudio();
    } else {
      resumeAudio();
    }
  };

  const handleSeek = (value: number) => {
    seekTo(value);
  };

  const canPlayPrevious = currentPlaylist && currentIndex > 0;
  const canPlayNext = currentPlaylist && currentIndex < currentPlaylist.length - 1;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
    >
      <View className={`flex-1 ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}>
        {/* En-tête */}
        <View className={`flex-row items-center justify-between p-4 ${
          isDarkMode ? 'bg-dark-800' : 'bg-white'
        }`}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons
              name="chevron-down"
              size={30}
              color={isDarkMode ? '#ffffff' : '#000000'}
            />
          </TouchableOpacity>
          
          <View className="flex-1 items-center">
            <Text className={`font-semibold text-base ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              En cours de lecture
            </Text>
          </View>
          
          {currentPlaylist && (
            <TouchableOpacity onPress={() => setShowPlaylist(!showPlaylist)}>
              <Ionicons
                name="list"
                size={24}
                color={isDarkMode ? '#22c55e' : '#eab308'}
              />
            </TouchableOpacity>
          )}
        </View>

        <View className="flex-1 p-6">
          {/* Image de couverture */}
          <View className="items-center mb-8">
            <Image
              source={{ uri: currentAudio.cover_url || getDefaultImage('audio') }}
              className="w-80 h-80 rounded-lg"
              resizeMode="cover"
            />
          </View>

          {/* Informations audio */}
          <View className="mb-8">
            <Text className={`text-xl font-bold text-center mb-2 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              {currentAudio.title}
            </Text>
            
            <Text className={`text-base text-center mb-4 ${
              isDarkMode ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {currentAudio.preacher?.name || 'Prédicateur inconnu'}
            </Text>
            
            {currentAudio.category && (
              <View className="items-center">
                <View className="bg-primary-500 px-4 py-2 rounded-full">
                  <Text className="text-white font-medium">
                    {currentAudio.category.name}
                  </Text>
                </View>
              </View>
            )}
          </View>

          {/* Barre de progression */}
          <View className="mb-6">
            <Slider
              style={{ width: '100%', height: 40 }}
              minimumValue={0}
              maximumValue={duration}
              value={progress}
              onSlidingComplete={handleSeek}
              minimumTrackTintColor={isDarkMode ? '#22c55e' : '#eab308'}
              maximumTrackTintColor={isDarkMode ? '#374151' : '#d1d5db'}
              thumbStyle={{ backgroundColor: isDarkMode ? '#22c55e' : '#eab308' }}
            />
            
            <View className="flex-row justify-between mt-2">
              <Text className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {formatTime(progress)}
              </Text>
              <Text className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {formatTime(duration)}
              </Text>
            </View>
          </View>

          {/* Contrôles */}
          <View className="flex-row items-center justify-center mb-8">
            <TouchableOpacity
              onPress={playPrevious}
              disabled={!canPlayPrevious}
              className={`w-12 h-12 rounded-full justify-center items-center mr-8 ${
                canPlayPrevious ? 'bg-primary-500' : 'bg-gray-400'
              }`}
            >
              <Ionicons
                name="play-skip-back"
                size={24}
                color="white"
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={handlePlayPause}
              disabled={isLoading}
              className="w-16 h-16 rounded-full bg-primary-500 justify-center items-center"
            >
              <Ionicons
                name={isLoading ? 'hourglass' : isPlaying ? 'pause' : 'play'}
                size={32}
                color="white"
              />
            </TouchableOpacity>
            
            <TouchableOpacity
              onPress={playNext}
              disabled={!canPlayNext}
              className={`w-12 h-12 rounded-full justify-center items-center ml-8 ${
                canPlayNext ? 'bg-primary-500' : 'bg-gray-400'
              }`}
            >
              <Ionicons
                name="play-skip-forward"
                size={24}
                color="white"
              />
            </TouchableOpacity>
          </View>

          {/* Description */}
          {currentAudio.description && (
            <View className={`p-4 rounded-lg ${
              isDarkMode ? 'bg-dark-800' : 'bg-white'
            }`}>
              <Text className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                {currentAudio.description}
              </Text>
            </View>
          )}
        </View>

        {/* Playlist */}
        {showPlaylist && currentPlaylist && (
          <View className={`absolute bottom-0 left-0 right-0 h-96 ${
            isDarkMode ? 'bg-dark-800' : 'bg-white'
          } border-t border-gray-200`}>
            <View className="p-4">
              <Text className={`font-semibold text-base mb-4 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Playlist ({currentPlaylist.length} audios)
              </Text>
              
              <ScrollView className="flex-1">
                {currentPlaylist.map((audio, index) => (
                  <TouchableOpacity
                    key={audio.id}
                    className={`flex-row items-center p-3 rounded-lg mb-2 ${
                      index === currentIndex ? 'bg-primary-100' : ''
                    }`}
                  >
                    <Text className={`text-sm mr-3 ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-600'
                    }`}>
                      {index + 1}
                    </Text>
                    
                    <View className="flex-1">
                      <Text className={`font-medium ${
                        isDarkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {audio.title}
                      </Text>
                      <Text className={`text-xs ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {audio.preacher?.name}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </View>
        )}
      </View>
    </Modal>
  );
};
