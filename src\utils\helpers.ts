export const formatTime = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

export const getDefaultImage = (type: 'preacher' | 'audio' | 'playlist'): string => {
  switch (type) {
    case 'preacher':
      return 'https://via.placeholder.com/150x150/22c55e/ffffff?text=Prédicateur';
    case 'audio':
      return 'https://via.placeholder.com/300x200/22c55e/ffffff?text=Audio';
    case 'playlist':
      return 'https://via.placeholder.com/300x200/eab308/ffffff?text=Playlist';
    default:
      return 'https://via.placeholder.com/300x200/22c55e/ffffff?text=Image';
  }
};

export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export const getCategoryColor = (categoryName: string): string => {
  const colors = {
    'Tafsir': 'bg-blue-500',
    'Fiqh': 'bg-green-500',
    'Aqida': 'bg-purple-500',
    'Hadith': 'bg-orange-500',
    'Sira': 'bg-red-500',
    'Adab': 'bg-teal-500',
  };
  
  return colors[categoryName as keyof typeof colors] || 'bg-gray-500';
};
