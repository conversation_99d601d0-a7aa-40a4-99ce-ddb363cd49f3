{"logs": [{"outputFile": "com.ballayoussouf.alfajr.app-mergeDebugResources-54:/values-zu/values-zu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b11e6b985dd17975fd05953637789a7\\transformed\\exoplayer-ui-2.19.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,293,522,726,823,914,998,1092,1187,1259,1330,1429,1529,1596,1660,1726,1806,1924,2048,2166,2241,2333,2407,2480,2574,2662,2725,2794,2847,2905,2957,3018,3078,3140,3205,3273,3343,3402,3470,3537,3605,3659,3727,3814,3901", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "288,517,721,818,909,993,1087,1182,1254,1325,1424,1524,1591,1655,1721,1801,1919,2043,2161,2236,2328,2402,2475,2569,2657,2720,2789,2842,2900,2952,3013,3073,3135,3200,3268,3338,3397,3465,3532,3600,3654,3722,3809,3896,3951"}, "to": {"startLines": "2,11,15,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,388,617,5391,5488,5579,5663,5757,5852,5924,5995,6094,6194,6261,6325,6391,6471,6589,6713,6831,6906,6998,7072,7145,7239,7327,7390,8114,8417,8475,8527,8588,8648,8710,8775,8843,8913,8972,9040,9107,9175,9229,9297,9384,9471", "endLines": "10,14,18,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,101,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121", "endColumns": "17,12,12,96,90,83,93,94,71,70,98,99,66,63,65,79,117,123,117,74,91,73,72,93,87,62,68,52,57,51,60,59,61,64,67,69,58,67,66,67,53,67,86,86,54", "endOffsets": "383,612,816,5483,5574,5658,5752,5847,5919,5990,6089,6189,6256,6320,6386,6466,6584,6708,6826,6901,6993,7067,7140,7234,7322,7385,7454,8162,8470,8522,8583,8643,8705,8770,8838,8908,8967,9035,9102,9170,9224,9292,9379,9466,9521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\bd4652fb8811503ef112af2338db59dd\\transformed\\extension-mediasession-2.19.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,136,221", "endColumns": "80,84,83", "endOffsets": "131,216,300"}, "to": {"startLines": "102,103,104", "startColumns": "4,4,4", "startOffsets": "8167,8248,8333", "endColumns": "80,84,83", "endOffsets": "8243,8328,8412"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7b2eacfe8e3674b51a5e18112d2898ac\\transformed\\exoplayer-core-2.19.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,183,245,314,391,471,560,641", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "119,178,240,309,386,466,555,636,705"}, "to": {"startLines": "92,93,94,95,96,97,98,99,100", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7459,7528,7587,7649,7718,7795,7875,7964,8045", "endColumns": "68,58,61,68,76,79,88,80,68", "endOffsets": "7523,7582,7644,7713,7790,7870,7959,8040,8109"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dfa8d219fb756bbf0447e8d2f088c459\\transformed\\browser-1.6.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "65,123,124,125", "startColumns": "4,4,4,4", "startOffsets": "5141,9629,9737,9849", "endColumns": "111,107,111,111", "endOffsets": "5248,9732,9844,9956"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7a9086f53045ff51dd925c6f1785f9fd\\transformed\\material-1.12.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,349,426,503,597,685,797,923,1004,1075,1142,1245,1320,1383,1475,1546,1611,1678,1750,1822,1876,1997,2056,2120,2174,2251,2383,2468,2545,2635,2715,2796,2945,3032,3115,3257,3349,3427,3483,3541,3607,3679,3756,3827,3910,3990,4069,4144,4223,4327,4417,4490,4584,4681,4755,4828,4927,4982,5066,5134,5222,5311,5373,5437,5500,5571,5680,5791,5894,6002,6062,6124,6206,6289,6365", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "266,344,421,498,592,680,792,918,999,1070,1137,1240,1315,1378,1470,1541,1606,1673,1745,1817,1871,1992,2051,2115,2169,2246,2378,2463,2540,2630,2710,2791,2940,3027,3110,3252,3344,3422,3478,3536,3602,3674,3751,3822,3905,3985,4064,4139,4218,4322,4412,4485,4579,4676,4750,4823,4922,4977,5061,5129,5217,5306,5368,5432,5495,5566,5675,5786,5889,5997,6057,6119,6201,6284,6360,6443"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,66,67,122,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,188,189,190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "821,3678,3756,3833,3910,4004,4822,4934,5060,5253,5324,9526,9961,10036,10099,10191,10262,10327,10394,10466,10538,10592,10713,10772,10836,10890,10967,11099,11184,11261,11351,11431,11512,11661,11748,11831,11973,12065,12143,12199,12257,12323,12395,12472,12543,12626,12706,12785,12860,12939,13043,13133,13206,13300,13397,13471,13544,13643,13698,13782,13850,13938,14027,14089,14153,14216,14287,14396,14507,14610,14718,14778,14840,15004,15087,15163", "endLines": "22,50,51,52,53,54,62,63,64,66,67,122,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,188,189,190", "endColumns": "12,77,76,76,93,87,111,125,80,70,66,102,74,62,91,70,64,66,71,71,53,120,58,63,53,76,131,84,76,89,79,80,148,86,82,141,91,77,55,57,65,71,76,70,82,79,78,74,78,103,89,72,93,96,73,72,98,54,83,67,87,88,61,63,62,70,108,110,102,107,59,61,81,82,75,82", "endOffsets": "987,3751,3828,3905,3999,4087,4929,5055,5136,5319,5386,9624,10031,10094,10186,10257,10322,10389,10461,10533,10587,10708,10767,10831,10885,10962,11094,11179,11256,11346,11426,11507,11656,11743,11826,11968,12060,12138,12194,12252,12318,12390,12467,12538,12621,12701,12780,12855,12934,13038,13128,13201,13295,13392,13466,13539,13638,13693,13777,13845,13933,14022,14084,14148,14211,14282,14391,14502,14605,14713,14773,14835,14917,15082,15158,15241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4bf03df1e227318d4ab7c4ce04613cbc\\transformed\\appcompat-1.7.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,320,432,520,623,738,817,894,985,1078,1173,1267,1367,1460,1555,1649,1740,1833,1914,2018,2121,2219,2326,2433,2538,2695,2791", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "208,315,427,515,618,733,812,889,980,1073,1168,1262,1362,1455,1550,1644,1735,1828,1909,2013,2116,2214,2321,2428,2533,2690,2786,2868"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,187", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "992,1100,1207,1319,1407,1510,1625,1704,1781,1872,1965,2060,2154,2254,2347,2442,2536,2627,2720,2801,2905,3008,3106,3213,3320,3425,3582,14922", "endColumns": "107,106,111,87,102,114,78,76,90,92,94,93,99,92,94,93,90,92,80,103,102,97,106,106,104,156,95,81", "endOffsets": "1095,1202,1314,1402,1505,1620,1699,1776,1867,1960,2055,2149,2249,2342,2437,2531,2622,2715,2796,2900,3003,3101,3208,3315,3420,3577,3673,14999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2eafbfcd37e5312020438d485ffc72b9\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "55,56,57,58,59,60,61,191", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4092,4190,4294,4393,4496,4602,4709,15246", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "4185,4289,4388,4491,4597,4704,4817,15342"}}]}]}