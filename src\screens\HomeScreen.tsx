import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, RefreshControl } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';
import { AudioCard } from '../components/AudioCard';
import { PreacherCard } from '../components/PreacherCard';
import { PlaylistCard } from '../components/PlaylistCard';
import { audioService, preacherService, playlistService } from '../services/supabase';
import { Audio, Preacher, Playlist } from '../types';

export const HomeScreen: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [latestAudios, setLatestAudios] = useState<Audio[]>([]);
  const [featuredPreachers, setFeaturedPreachers] = useState<Preacher[]>([]);
  const [featuredPlaylists, setFeaturedPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [audios, preachers, playlists] = await Promise.all([
        audioService.getLatest(5),
        preacherService.getAll(),
        playlistService.getAll()
      ]);
      
      setLatestAudios(audios);
      setFeaturedPreachers(preachers.slice(0, 3));
      setFeaturedPlaylists(playlists.slice(0, 3));
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <View className={`flex-1 justify-center items-center ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}>
        <Text className={`text-base ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Chargement...
        </Text>
      </View>
    );
  }

  return (
    <ScrollView
      className={`flex-1 ${
        isDarkMode ? 'bg-dark-900' : 'bg-gray-50'
      }`}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      <View className="p-4">
        {/* En-tête */}
        <View className="mb-6">
          <Text className={`text-xl font-bold mb-2 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Al Fajr
          </Text>
          <Text className={`text-sm ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            L'enseignement de nos prédécesseurs pieux
          </Text>
        </View>

        {/* Derniers audios */}
        <View className="mb-6">
          <Text className={`text-lg font-semibold mb-3 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Derniers audios
          </Text>
          {latestAudios.map((audio) => (
            <AudioCard key={audio.id} audio={audio} />
          ))}
        </View>

        {/* Prédicateurs à découvrir */}
        <View className="mb-6">
          <Text className={`text-lg font-semibold mb-3 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Prédicateurs à découvrir
          </Text>
          {featuredPreachers.map((preacher) => (
            <PreacherCard key={preacher.id} preacher={preacher} />
          ))}
        </View>

        {/* Playlists recommandées */}
        <View className="mb-6">
          <Text className={`text-lg font-semibold mb-3 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Playlists recommandées
          </Text>
          {featuredPlaylists.map((playlist) => (
            <PlaylistCard key={playlist.id} playlist={playlist} />
          ))}
        </View>
      </View>
    </ScrollView>
  );
};
