{"installationFolder": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-worklets", "packageInfo": {"packageName": "react-native-worklets", "packageVersion": "0.5.0", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "worklets", "moduleHeaders": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\build\\intermediates\\cxx\\Debug\\1o396h4d\\obj\\arm64-v8a\\libworklets.so", "abiAndroidGradleBuildJsonFile": "D:\\Al Fajr\\Version\\Alpha\\al-fajr_alpha\\al-fajr\\node_modules\\react-native-worklets\\android\\.cxx\\Debug\\1o396h4d\\arm64-v8a\\android_gradle_build.json"}]}]}}