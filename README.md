# Al Fajr - Application Audio Islamique

Une application mobile React Native pour écouter des conférences, cours et podcasts de différents prédicateurs.

## 🎯 Concept

**Al Fajr** - "L'enseignement de nos prédécesseurs pieux"

Application gratuite sans authentification, destinée aux utilisateurs africains, particulièrement au Cameroun.

## ✨ Fonctionnalités

- **Écran d'accueil** : Derniers audios, prédicateurs à découvrir, playlists recommandées
- **Prédicateurs** : Liste complète avec photos et biographies
- **Catégories** : Tafsir, Fiqh, Aqida, Hadith, etc.
- **Playlists** : Collections thématiques d'audios
- **Lecteur audio** : Mini-lecteur persistant + lecteur plein écran
- **Mode sombre/clair** : Thème adaptatif
- **Navigation intuitive** : Onglets en bas de l'écran

## 🛠️ Technologies

- **React Native** avec Expo
- **TypeScript** pour la sécurité des types
- **NativeWind** (Tailwind CSS) pour le styling
- **React Navigation** pour la navigation
- **Expo AV** pour la lecture audio
- **Supabase** pour le backend (PostgreSQL + Storage)
- **AsyncStorage** pour la persistance locale

## 📱 Installation

1. **Cloner le projet**
   ```bash
   git clone <repository-url>
   cd al-fajr
   ```

2. **Installer les dépendances**
   ```bash
   npm install
   ```

3. **Configurer Supabase**
   - Créer un projet Supabase
   - Exécuter le script SQL dans `database/schema.sql`
   - Créer un fichier `.env` à la racine :
   ```env
   EXPO_PUBLIC_SUPABASE_URL=votre_url_supabase
   EXPO_PUBLIC_SUPABASE_ANON_KEY=votre_cle_anonyme_supabase
   ```

4. **Lancer l'application**
   ```bash
   npm start
   ```

## 🗄️ Base de données

Le schéma comprend :
- `preachers` : Prédicateurs avec photos et biographies
- `categories` : Catégories de contenu
- `audios` : Fichiers audio avec métadonnées
- `playlists` : Collections d'audios
- `playlist_audios` : Relation many-to-many

## 🎨 Design

- **Thème sombre** par défaut avec accents verts et dorés
- **Interface moderne** et élégante
- **Lecteur audio professionnel** avec contrôles avancés
- **Navigation fluide** avec animations

## 📁 Structure du projet

```
src/
├── components/          # Composants réutilisables
├── contexts/           # Contextes React (thème, lecteur)
├── navigation/         # Configuration de navigation
├── screens/           # Écrans de l'application
├── services/          # Services API (Supabase)
├── types/             # Types TypeScript
└── utils/             # Fonctions utilitaires
```

## 🚀 Déploiement

1. **Build pour Android**
   ```bash
   expo build:android
   ```

2. **Build pour iOS**
   ```bash
   expo build:ios
   ```

## 🔧 Configuration avancée

### Variables d'environnement
- `EXPO_PUBLIC_SUPABASE_URL` : URL de votre projet Supabase
- `EXPO_PUBLIC_SUPABASE_ANON_KEY` : Clé anonyme Supabase

### Personnalisation
- Modifier `tailwind.config.js` pour les couleurs
- Ajuster `app.config.js` pour les métadonnées
- Personnaliser les icônes dans `assets/`

## 📄 Licence

MIT License - Voir le fichier `LICENSE` pour plus de détails.

## 🤝 Contribution

Les contributions sont les bienvenues ! Veuillez :
1. Fork le projet
2. Créer une branche feature
3. Commiter vos changements
4. Pousser vers la branche
5. Ouvrir une Pull Request

## 📞 Support

Pour toute question ou problème :
- Ouvrir une issue sur GitHub
- Contacter l'équipe de développement

---

**Al Fajr** - L'enseignement de nos prédécesseurs pieux 🌅
