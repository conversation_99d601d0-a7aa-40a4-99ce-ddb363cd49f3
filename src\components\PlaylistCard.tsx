import React from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { Playlist } from '../types';
import { useTheme } from '../contexts/ThemeContext';
import { getDefaultImage, truncateText } from '../utils/helpers';

interface PlaylistCardProps {
  playlist: Playlist;
  onPress?: () => void;
}

export const PlaylistCard: React.FC<PlaylistCardProps> = ({ playlist, onPress }) => {
  const { isDarkMode } = useTheme();

  return (
    <TouchableOpacity
      onPress={onPress}
      className={`p-4 rounded-lg mb-3 ${
        isDarkMode ? 'bg-dark-800' : 'bg-white'
      } shadow-sm`}
      style={{ elevation: 2 }}
    >
      <View className="flex-row items-center">
        <Image
          source={{ uri: playlist.cover_url || getDefaultImage('playlist') }}
          className="w-20 h-20 rounded-lg mr-4"
          resizeMode="cover"
        />
        
        <View className="flex-1">
          <Text
            className={`font-semibold text-base mb-1 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}
            numberOfLines={2}
          >
            {playlist.name}
          </Text>
          
          {playlist.description && (
            <Text
              className={`text-sm mb-2 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}
              numberOfLines={2}
            >
              {truncateText(playlist.description, 80)}
            </Text>
          )}
          
          {playlist.preacher && (
            <View className="flex-row items-center">
              <View className="bg-gold-100 px-2 py-1 rounded-full">
                <Text className="text-gold-700 text-xs font-medium">
                  {playlist.preacher.name}
                </Text>
              </View>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};
